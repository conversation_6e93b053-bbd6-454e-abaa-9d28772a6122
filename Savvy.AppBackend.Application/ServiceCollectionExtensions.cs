using Microsoft.Extensions.DependencyInjection;
using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        services.AddScoped<IDateTimeService, DateTimeService>();
        services.AddScoped<IStreamService, StreamService>();
        services.AddScoped<IUserGameService, UserGameService>();
        services.AddScoped<IUserProfileService, UserProfileService>();
        services.AddScoped<IUserRoleService, UserRoleService>();
        services.AddScoped<IPushNotificationService, PushNotificationService>();
        services.AddScoped<ILeaderboardService, LeaderboardService>();
        services.AddScoped<IGameService, GameService>();
        services.AddScoped<IPowerupService, PowerupService>();

        return services;
    }
}
