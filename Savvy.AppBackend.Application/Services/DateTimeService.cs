using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services
{
    public class DateTimeService : IDateTimeService
    {
        /// <inheritdoc/>
        public TimeRange GetStartAndEndOfTheGameWeek()
        {
            var daysInWeek = 5;
            return CalculateStartAndEndOfTheWeekWithGivenDays(daysInWeek);
        }

        /// <inheritdoc/>
        public TimeRange GetStartAndEndOfTheFullWeek()
        {
            var daysInWeek = 7;
            return CalculateStartAndEndOfTheWeekWithGivenDays(daysInWeek);
        }

        /// <inheritdoc/>
        public TimeRange GetStartAndEndOfCurrentMonth()
        {
            DateTime today = DateTime.Today;
            int daysInMonth = DateTime.DaysInMonth(today.Year, today.Month);
            DateTime startDate = new DateTime(today.Year, today.Month, 1);
            DateTime endDate = new DateTime(today.Year, today.Month, daysInMonth);

            return new TimeRange
            {
                StartOfRange = startDate,
                EndOfRange = endDate
            };
        }

        private TimeRange CalculateStartAndEndOfTheWeekWithGivenDays(int daysInWeek)
        {
            var today = DateTime.Parse(DateTime.Today.ToString("o"));
            int currentDayOfWeek = (int)today.DayOfWeek;
            int daysToSubtract = (currentDayOfWeek - (int)DayOfWeek.Sunday + 7) % 7;

            var startDateOfWeek = today.AddDays(-daysToSubtract);

            var endDateOfWeek = startDateOfWeek.AddDays(daysInWeek).Date.AddTicks(-1);

            return new TimeRange
            {
                StartOfRange = startDateOfWeek,
                EndOfRange = endDateOfWeek
            };
        }
    }
}
