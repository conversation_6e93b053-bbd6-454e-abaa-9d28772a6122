using Mapster;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.GameModels;
using Savvy.AppBackend.Domain.Models.HomeModels;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services
{
    public class GameService : IGameService
    {
        private readonly IStreamService _streamService;
        private readonly IUserGameService _userGameService;

        public GameService(IUserGameService userGameService, IStreamService streamService)
        {
            _userGameService = userGameService;
            _streamService = streamService;
        }

        public async Task<WeeklyGamesAndDetails> GetWeeklyGamesAndDetailsAsync(string userId)
        {
            var weeklyStreams = await _streamService.GetWeeklyStreams();

            var userGameDetails = await _userGameService.GetWeeklyUserGameResults();
            
            var homeDetails = new WeeklyGamesAndDetails
            {
                WeeklyGames = weeklyStreams.Select(e =>
                {
                    var userGameDetail = userGameDetails.FirstOrDefault(d => string.Equals(d.StreamId, e.Id));
                    var game = e.Adapt<GameDetails>();
                    game.XpEarned = userGameDetail?.GameScore ?? 0;

                    var isUserQualifiedToMoneyGame = false;
                    if (e.Type == StreamType.PrizeGame)
                    {
                        isUserQualifiedToMoneyGame = userGameDetails.Any(e => e.IsUserQualifiedToMoneyGame);
                    }
                    else if (userGameDetail is not null)
                    {
                        isUserQualifiedToMoneyGame = userGameDetail.IsUserQualifiedToMoneyGame;
                    }
                    game.IsUserQualifiedToMoneyGame = isUserQualifiedToMoneyGame;

                    return game;
                })
            };

            

            return homeDetails;
        }
    }
}
