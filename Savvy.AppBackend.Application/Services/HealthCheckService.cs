using System.Diagnostics;
using FirebaseAdmin.Auth;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;
using FirebaseAdmin;

namespace Savvy.AppBackend.Application.Services;

public class HealthCheckService : IHealthCheckService
{
    private readonly CosmosClient _cosmosClient;
    private readonly FirebaseAuth _firebaseAuth;
    private readonly TelemetryClient _telemetryClient;
    private readonly ILogger<HealthCheckService> _logger;
    private readonly CosmosDbOptions _cosmosDbOptions;
    private readonly ICosmosDbRepository _cosmosDbRepository;

    public HealthCheckService(
        CosmosClient cosmosClient,
        FirebaseAuth firebaseAuth,
        TelemetryClient telemetryClient,
        ILogger<HealthCheckService> logger,
        Microsoft.Extensions.Options.IOptions<CosmosDbOptions> cosmosDbOptions,
        ICosmosDbRepository cosmosDbRepository)
    {
        _cosmosClient = cosmosClient;
        _firebaseAuth = firebaseAuth;
        _telemetryClient = telemetryClient;
        _logger = logger;
        _cosmosDbOptions = cosmosDbOptions.Value;
        _cosmosDbRepository = cosmosDbRepository;
    }

    public async Task<OverallHealthCheck> GetOverallHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var checkedAt = DateTime.UtcNow;
        
        var healthChecks = new List<Task<ServiceHealthCheck>>
        {
            CheckCosmosDbHealthAsync(),
            CheckFirebaseHealthAsync(),
            CheckApplicationInsightsHealthAsync()
        };

        ServiceHealthCheck[] results = await Task.WhenAll(healthChecks);
        stopwatch.Stop();

        var overallStatus = DetermineOverallStatus(results);
        var description = GenerateOverallDescription(results, overallStatus);

        return new OverallHealthCheck
        {
            Status = overallStatus,
            Description = description,
            CheckedAt = checkedAt,
            TotalResponseTime = stopwatch.Elapsed,
            Services = results.ToList()
        };
    }

    public async Task<ServiceHealthCheck> CheckCosmosDbHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var checkedAt = DateTime.UtcNow;
        var serviceName = "CosmosDB";

        try
        {
            // Try to read account properties to verify connection
            var response = await _cosmosClient.ReadAccountAsync();
            
            // Try to get database to verify it exists
            var database = _cosmosClient.GetDatabase(_cosmosDbOptions.DatabaseId);
            var accountProperties = await _cosmosClient.ReadAccountAsync();
            
            stopwatch.Stop();

            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Healthy,
                Description = "CosmosDB connection is healthy",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["DatabaseId"] = _cosmosDbOptions.DatabaseId,
                    ["AccountId"] = accountProperties?.Id ?? "Unknown",
                    ["ConsistencyLevel"] = accountProperties != null && accountProperties.Consistency != null
                        ? accountProperties.Consistency.DefaultConsistencyLevel.ToString()
                        : "Unknown"
                }
            };
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
        {
            stopwatch.Stop();
            _logger.LogWarning("CosmosDB health check failed due to rate limiting: {Message}", ex.Message);
            
            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Degraded,
                Description = "CosmosDB is experiencing rate limiting",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["Error"] = "Rate limited",
                    ["RetryAfter"] = ex.RetryAfter?.TotalSeconds ?? 0
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "CosmosDB health check failed: {Message}", ex.Message);
            
            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Unhealthy,
                Description = $"CosmosDB connection failed: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["ExceptionType"] = ex.GetType().Name
                }
            };
        }
    }

    public async Task<ServiceHealthCheck> CheckFirebaseHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var checkedAt = DateTime.UtcNow;
        var serviceName = "Firebase";

        try
        {
            // Attempt to create a token as a safe, non-invasive health check
            await _firebaseAuth.CreateCustomTokenAsync("firebase-healthcheck-dummy-user");

            stopwatch.Stop();

            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Healthy,
                Description = "Firebase connection is healthy",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["ProjectId"] = FirebaseApp.DefaultInstance.Options.ProjectId ?? "Unknown"
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Firebase health check failed: {Message}", ex.Message);

            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Unhealthy,
                Description = $"Firebase connection failed: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["ExceptionType"] = ex.GetType().Name
                }
            };
        }
    }

    public async Task<ServiceHealthCheck> CheckApplicationInsightsHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        var checkedAt = DateTime.UtcNow;
        var serviceName = "Application Insights";

        try
        {
            // Send a test telemetry event to verify Application Insights connection
            var testEvent = new EventTelemetry("HealthCheck")
            {
                Timestamp = DateTimeOffset.UtcNow
            };
            testEvent.Properties["Source"] = "HealthCheckService";
            testEvent.Properties["CheckId"] = Guid.NewGuid().ToString();

            _telemetryClient.TrackEvent(testEvent);
            
            // Flush to ensure the telemetry is sent
            _telemetryClient.Flush();
            
            // Wait a short time for the flush to complete
            await Task.Delay(100);
            
            stopwatch.Stop();

            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Healthy,
                Description = "Application Insights connection is healthy",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["InstrumentationKey"] = _telemetryClient.InstrumentationKey ?? "Not configured",
                    ["IsEnabled"] = !_telemetryClient.IsEnabled() ? "Disabled" : "Enabled"
                }
            };
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Application Insights health check failed: {Message}", ex.Message);
            
            return new ServiceHealthCheck
            {
                ServiceName = serviceName,
                Status = HealthStatus.Degraded,
                Description = $"Application Insights may have issues: {ex.Message}",
                ResponseTime = stopwatch.Elapsed,
                CheckedAt = checkedAt,
                Data = new Dictionary<string, object>
                {
                    ["Error"] = ex.Message,
                    ["ExceptionType"] = ex.GetType().Name,
                    ["Note"] = "Application Insights failures are often non-blocking"
                }
            };
        }
    }

    private static HealthStatus DetermineOverallStatus(ServiceHealthCheck[] results)
    {
        if (results.Any(r => r.Status == HealthStatus.Unhealthy))
            return HealthStatus.Unhealthy;
        
        if (results.Any(r => r.Status == HealthStatus.Degraded))
            return HealthStatus.Degraded;
        
        return HealthStatus.Healthy;
    }

    private static string GenerateOverallDescription(ServiceHealthCheck[] results, HealthStatus overallStatus)
    {
        var healthyCount = results.Count(r => r.Status == HealthStatus.Healthy);
        var degradedCount = results.Count(r => r.Status == HealthStatus.Degraded);
        var unhealthyCount = results.Count(r => r.Status == HealthStatus.Unhealthy);

        return overallStatus switch
        {
            HealthStatus.Healthy => $"All {results.Length} services are healthy",
            HealthStatus.Degraded => $"{healthyCount} healthy, {degradedCount} degraded, {unhealthyCount} unhealthy services",
            HealthStatus.Unhealthy => $"{unhealthyCount} critical service(s) are unhealthy",
            _ => "Unknown status"
        };
    }
}
