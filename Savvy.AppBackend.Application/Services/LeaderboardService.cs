using Mapster;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.Enums;
using Savvy.AppBackend.Domain.Models.Leaderboard;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services
{
    public class LeaderboardService : ILeaderboardService
    {
        private readonly IUserGameService _userGameService;
        private readonly IUserProfileService _userProfileService;

        public LeaderboardService(IUserGameService userGameService, IUserProfileService userProfileService)
        {
            _userGameService = userGameService;
            _userProfileService = userProfileService;
        }

        public async Task<IEnumerable<LeaderboardEntry>> GetLeaderboardAsync(TimeFilter timeFilter, int limit, int skip)
        {
            if (limit < 0 || skip < 0)
            {
                throw new ValidationException("Take or skip argurent is invalid");
            }

            var userScoreData = await GetUserGameDetailsByTimeFilter(timeFilter, limit, skip);
            var userIds = userScoreData.Select(e => e.UserId).ToArray();

            var users = await _userProfileService.GetListOfUsersByIdAsync(userIds);

            return users.Select(user =>
            {
                var userScore = userScoreData.First(x => x.UserId == user.Id);
                var leaderboardEntry = user.Adapt<LeaderboardEntry>();
                leaderboardEntry.Rank = userScore.Rank;
                leaderboardEntry.TotalScore = userScore.TotalScore;
                leaderboardEntry.UserId = userScore.UserId;
                return leaderboardEntry;
            }).OrderBy(e => e.Rank);
        }

        private async Task<IEnumerable<UserScoreDetails>> GetUserGameDetailsByTimeFilter(TimeFilter timeFilter, int limit, int skip)
        {
            switch (timeFilter)
            {
                case TimeFilter.None:
                case TimeFilter.Weekly:
                    return await GetWeeklyUserGameDetails(limit, skip);
                case TimeFilter.Monthly:
                    return await GetMonthlyUserGameDetails(limit, skip);
                case TimeFilter.All:
                    return await _userGameService.GetAllTimeUserGameDetails(limit, skip);
                default:
                    return Array.Empty<UserScoreDetails>();
            }
        }

        private async Task<IEnumerable<UserScoreDetails>> GetWeeklyUserGameDetails(int limit, int skip)
        {
            var weeklyUserGameDetails = await _userGameService.GetWeeklyUserGameResults();
            return TransformUserGameDetails(weeklyUserGameDetails, limit, skip);
        }

        private async Task<IEnumerable<UserScoreDetails>> GetMonthlyUserGameDetails(int limit, int skip)
        {
            var monthlyUserGameDetails = await _userGameService.GetMonthlyUserGameResults();
            return TransformUserGameDetails(monthlyUserGameDetails, limit, skip);
        }

        private IEnumerable<UserScoreDetails> TransformUserGameDetails(IEnumerable<UserGameDetails> userGameDetails, int limit, int skip)
        {
            var groupedGameDetails = userGameDetails.GroupBy(e => e.UserId, e => e.GameScore, (userId, listOfPoints) => new UserScoreDetails
            {
                UserId = userId,
                TotalScore = listOfPoints.Sum()
            }).OrderByDescending(e => e.TotalScore).Skip(skip).Take(limit).Select((e, index) =>
            {
                return new UserScoreDetails
                {
                    Rank = skip + index + 1,
                    UserId = e.UserId,
                    TotalScore = e.TotalScore,
                };
            });

            return groupedGameDetails;
        }
    }
}