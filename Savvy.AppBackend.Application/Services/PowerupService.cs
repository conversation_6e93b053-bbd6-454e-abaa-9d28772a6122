using Mapster;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models.PowerupModels;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services
{
    public class PowerupService : IPowerupService
    {
        private readonly ICosmosDbRepository _cosmosDbRepository;

        private const string ContainerId = CosmosDbContainers.PowerupsContainerId;

        public PowerupService(ICosmosDbRepository cosmosDbRepository)
        {
            _cosmosDbRepository = cosmosDbRepository;
        }

        public async Task<IEnumerable<PowerupShortInfo>> UpdatePowerupsNumber(IEnumerable<PowerupShortInfo> powerups, string userId)
        {
            if (!powerups.Any())
            {
                throw new ValidationException("Request doesn't contains powerups data to add.");
            }

            var usersPowerups = await _cosmosDbRepository.ListItemsAsync<PowerupDocument>(
                ContainerId,
                x => x.Where(e => e.UserId == userId)
            );

            var mappedPowerups = powerups.Select(e =>
            {
                var existingPowerup = usersPowerups.FirstOrDefault(x => x.PowerupId == e.PowerupId);
                var resultPowerupNumber = existingPowerup is not null ? existingPowerup.Count + e.Count : e.Count;

                var document = new PowerupDocument
                {
                    Id = existingPowerup?.Id ?? Guid.NewGuid().ToString(),
                    PowerupId = e.PowerupId,
                    Count = resultPowerupNumber < 0 ? 0 : resultPowerupNumber,
                    UserId = userId,
                    PartitionKey = userId,
                };

                return document;
            });

            foreach (var item in mappedPowerups)
            {
                var addedItem = await _cosmosDbRepository.UpsertItemAsync(ContainerId, item);
            }

            return mappedPowerups.Adapt<IEnumerable<PowerupShortInfo>>();
        }

        public async Task<IEnumerable<PowerupShortInfo>> GetUsersPowerups(string userId)
        {
            var powerups = await _cosmosDbRepository.ListItemsAsync<PowerupDocument>(
                ContainerId,
                x => x.Where(e => e.UserId == userId)
            );

            return powerups.Adapt<IEnumerable<PowerupShortInfo>>();
        }
    }
}
