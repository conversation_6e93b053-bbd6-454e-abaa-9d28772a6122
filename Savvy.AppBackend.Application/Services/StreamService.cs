using Mapster;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;
using StreamStatus = Savvy.AppBackend.Data.CosmosDb.Documents.StreamStatus;

namespace Savvy.AppBackend.Application.Services;

public class StreamService : IStreamService
{
    private readonly ICosmosDbRepository _cosmosDbRepository;
    private readonly IDateTimeService _dateTimeService;

    private const string ContainerId = CosmosDbContainers.StreamContainerId;

    public StreamService(ICosmosDbRepository cosmosDbRepository, IDateTimeService dateTimeService)
    {
        _cosmosDbRepository = cosmosDbRepository;
        _dateTimeService = dateTimeService;
    }

    public async Task<IEnumerable<StreamDetail>> GetScheduledStreamsAsync()
    {
        var scheduledStreamsDocuments = await _cosmosDbRepository.ListItemsAsync<StreamDocument>(ContainerId,
            q => q.Where(u => u.Status == StreamStatus.Scheduled));

        return scheduledStreamsDocuments.Adapt<IEnumerable<StreamDetail>>();
    }

    public async Task<StreamDetail> CreateStreamAsync(StreamDetail streamDetail)
    {
        var streamDetailDocument = streamDetail.Adapt<StreamDocument>();
        streamDetailDocument.Id = Guid.NewGuid().ToString();
        streamDetailDocument.PartitionKey = streamDetail.HostId;
        streamDetailDocument.Status = StreamStatus.Scheduled;
        streamDetailDocument.StartDate = streamDetailDocument.StartDate;

        var newStreamDetailDocument = await _cosmosDbRepository.UpsertItemAsync(ContainerId, streamDetailDocument);

        return newStreamDetailDocument.Adapt<StreamDetail>();
    }

    public async Task UpdateStreamAsync(string id, string hostId, StreamDetail streamDetail)
    {
        var streamDocument = await _cosmosDbRepository.ReadItemAsync<StreamDocument>(ContainerId, id, hostId) ?? throw new NotFoundException(nameof(StreamDetail), id);
        streamDocument.Title = streamDetail.Title;
        streamDocument.Status = (StreamStatus)streamDetail.Status;
        streamDocument.StartDate = streamDetail.StartDate;

        await _cosmosDbRepository.UpsertItemAsync(ContainerId, streamDocument);
    }

    public async Task UpdateStreamStatus(string id, string hostId, Domain.Models.StreamStatus streamStatus)
    {
        var streamDocument = await _cosmosDbRepository.ReadItemAsync<StreamDocument>(ContainerId, id, hostId) ?? throw new NotFoundException(nameof(StreamDetail), id);

        streamDocument.Status = (StreamStatus)streamStatus;

        await _cosmosDbRepository.UpsertItemAsync(ContainerId, streamDocument);
    }

    public async Task<IEnumerable<StreamDetail>> GetWeeklyStreams()
    {
        var startAndEndOfTheRange = _dateTimeService.GetStartAndEndOfTheGameWeek();

        var streamsInThisWeek = await _cosmosDbRepository.ListItemsAsync<StreamDocument>(ContainerId,
        q => q.Where(e => e.StartDate >= startAndEndOfTheRange.StartOfRange && e.StartDate <= startAndEndOfTheRange.EndOfRange));
        streamsInThisWeek.ForEach(e => e.StartDate = e.StartDate.ToUniversalTime());

        return streamsInThisWeek.OrderBy(e => e.StartDate).Select(e => e.Adapt<StreamDetail>());
    }
}
