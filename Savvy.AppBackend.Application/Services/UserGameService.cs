using Mapster;
using Microsoft.Extensions.Logging;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.Leaderboard;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services
{
    public class UserGameService : IUserGameService
    {
        private const string ContainerId = CosmosDbContainers.UserGameDetailsContainerId;
        private const string PartitionKey = "details";

        private readonly ICosmosDbRepository _cosmosDbRepository;
        private readonly IDateTimeService _dateTimeService;

        public UserGameService(ICosmosDbRepository cosmosDbRepository, IDateTimeService dateTimeService)
        {
            _cosmosDbRepository = cosmosDbRepository;
            _dateTimeService = dateTimeService;
        }

        public async Task<UserGameDetails> AddUserGameDetailsAsync(UserGameDetails userGameDetails)
        {
            var existingRecords = await _cosmosDbRepository.ListItemsAsync<UserGameDetailsDocument>(ContainerId,
            q => q.Where(e => e.StreamId == userGameDetails.StreamId && e.UserId == userGameDetails.UserId));

            if (existingRecords.Count != 0)
            {
                throw new ValidationException("Record with the same stream id and user id already exists");
            }

            var userStreamDetailsDocument = userGameDetails.Adapt<UserGameDetailsDocument>();

            userStreamDetailsDocument.Id = Guid.NewGuid().ToString();
            userStreamDetailsDocument.PartitionKey = PartitionKey;

            var newUserGameDetailsDocument = await _cosmosDbRepository.UpsertItemAsync(ContainerId, userStreamDetailsDocument);

            return newUserGameDetailsDocument.Adapt<UserGameDetails>();
        }

        public async Task<IEnumerable<UserGameDetails>> GetUserGameDetailsByStreamsAndUser(string[] streamIds, string userId)
        {
            var userGameDetails = await _cosmosDbRepository.ListItemsAsync<UserGameDetailsDocument>(
                ContainerId,
                e => e.Where(x => streamIds.Contains(x.StreamId) && x.UserId == userId)
            );

            return userGameDetails.Select(e => e.Adapt<UserGameDetails>());
        }

        public async Task<IEnumerable<UserGameDetails>> GetUserGameDetailsByStreams(string[] streamIds)
        {
            var userGameDetails = await _cosmosDbRepository.ListItemsAsync<UserGameDetailsDocument>(
                ContainerId,
                e => e.Where(x => streamIds.Contains(x.StreamId))
            );

            return userGameDetails.Select(e => e.Adapt<UserGameDetails>());
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<UserScoreDetails>> GetAllTimeUserGameDetails(int limit, int skip)
        {
            var result = await _cosmosDbRepository.CallStoredProcedure<UserScoreDetails[]>(ContainerId, "getGroupedDataByUserId", PartitionKey, PartitionKey, skip.ToString(), limit.ToString());
            return result;
        }

        public async Task<IEnumerable<UserGameDetails>> GetWeeklyUserGameResults()
        {
            var startAndEndOfTheWeek = _dateTimeService.GetStartAndEndOfTheGameWeek();

            return await GetUserGameResultsByTimeRange(startAndEndOfTheWeek);
        }
        public async Task<IEnumerable<UserGameDetails>> GetMonthlyUserGameResults()
        {
            var startAndEndOfTheCurrentMoth = _dateTimeService.GetStartAndEndOfCurrentMonth();

            return await GetUserGameResultsByTimeRange(startAndEndOfTheCurrentMoth);
        }


        private async Task<IEnumerable<UserGameDetails>> GetUserGameResultsByTimeRange(TimeRange startAndEndOfRange)
        {
            var userGameDetails = await _cosmosDbRepository.ListItemsAsync<UserGameDetailsDocument>(
                ContainerId,
                e => e.Where(x => x.StreamStartDate >= startAndEndOfRange.StartOfRange && x.StreamStartDate <= startAndEndOfRange.EndOfRange)
            );

            return userGameDetails.Adapt<IEnumerable<UserGameDetails>>();
        }
    }
}
