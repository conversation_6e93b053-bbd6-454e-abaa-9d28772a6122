using Mapster;
using Microsoft.Azure.Cosmos;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Application.Services;

public class UserProfileService : IUserProfileService
{
    private readonly ICosmosDbRepository _cosmosDbRepository;
    private readonly IStreamService _streamService;
    private readonly IUserGameService _userGameService;

    private const string ContainerId = CosmosDbContainers.UsersContainerId;

    public UserProfileService(ICosmosDbRepository cosmosDbRepository, IStreamService streamService, IUserGameService userGameService)
    {
        _cosmosDbRepository = cosmosDbRepository;
        _streamService = streamService;
        _userGameService = userGameService;
    }

    public async Task<UserProfile> CreateUserProfileAsync(UserProfile userProfile)
    {
        var user = await _cosmosDbRepository.ListItemsAsync<UserProfileDocument>(ContainerId,
            q => q.Where(u => u.Username == userProfile.Username || u.Id == userProfile.Id));

        if (user.Count != 0)
        {
            throw new ValidationException("User with the same name or id already exists");
        }

        var userProfileDocument = userProfile.Adapt<UserProfileDocument>();
        userProfileDocument.Id = userProfile.Id;
        userProfileDocument.PartitionKey = userProfile.Id;
        userProfileDocument.ImageUrl = userProfile.ImageUrl;
        userProfileDocument.LastTermsAcceptedAt = DateTimeOffset.UtcNow;

        var newUserProfileDocument = await _cosmosDbRepository.UpsertItemAsync(ContainerId, userProfileDocument);

        return newUserProfileDocument.Adapt<UserProfile>();
    }

    public async Task<IEnumerable<UserProfileShortInfo>> GetListOfUsersByIdAsync(string[] userIds)
    {
        var users = await _cosmosDbRepository.ListItemsAsync<UserProfileDocument>(
            ContainerId,
            e => e.Where(x => userIds.Contains(x.Id))
        );

        return users.Select(e => e.Adapt<UserProfileShortInfo>());
    }

    public async Task<UserProfile> GetUserProfileByIdAsync(string userId)
    {
        try
        {
            var userProfileDocument =
                await _cosmosDbRepository.ReadItemAsync<UserProfileDocument>(ContainerId, userId, userId);

            var userProfile = userProfileDocument.Adapt<UserProfile>();

            userProfile.Score = await GetUserWeeklyScore(userProfile.Id);

            return userProfile;
        }
        catch (CosmosException e) when (e.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            throw new NotFoundException(nameof(UserProfile), userId);
        }
    }

    public async Task<UserProfile> GetUserProfileByUsernameAsync(string username)
    {
        var userProfileDocument = await _cosmosDbRepository.ListItemsAsync<UserProfileDocument>(
            ContainerId, q => q.Where(u => u.Username == username));

        if (userProfileDocument.Count == 0)
        {
            throw new NotFoundException(nameof(UserProfile), username);
        }

        var userProfile = userProfileDocument.First().Adapt<UserProfile>();

        userProfile.Score = await GetUserWeeklyScore(userProfile.Id);

        return userProfile;
    }

    private async Task<int> GetUserWeeklyScore(string userId)
    {
        var weeklyStreams = await _streamService.GetWeeklyStreams();
        var streamIds = weeklyStreams.Select(e => e.Id);
        var userGameDetails = await _userGameService.GetUserGameDetailsByStreamsAndUser(streamIds.ToArray(), userId);
        return userGameDetails.Sum(e => e.GameScore);
    }

    public async Task<UserProfile> UpdateSparksNumber(int sparksToUpdate, string userId)
    {
        try
        {
            var userProfileDocument = await _cosmosDbRepository.ReadItemAsync<UserProfileDocument>(ContainerId, userId, userId);

            var newSparksNumber = userProfileDocument.Sparks + sparksToUpdate;
            if (newSparksNumber < 0)
            {
                throw new ValidationException("The result of sparks adding is less then zero. Sparks number can't be less then 0.");
            }

            userProfileDocument.Sparks = newSparksNumber;

            var updatedUserProfile = await _cosmosDbRepository.UpsertItemAsync(ContainerId, userProfileDocument);

            return updatedUserProfile.Adapt<UserProfile>();
        }
        catch (ValidationException)
        {
            throw;
        }
        catch (CosmosException e) when (e.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            throw new NotFoundException(nameof(UserProfile), userId);
        }
    }
}