using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Infrastructure;

namespace Savvy.AppBackend.Application.Services
{
    public class UserRoleService : IUserRoleService
    {
        private readonly IFirebaseAdminService _firebaseAdminService;

        public UserRoleService(IFirebaseAdminService firebaseAdminService)
        {
            _firebaseAdminService = firebaseAdminService;
        }

        public async Task<UserRole> PromoteUserToAdmin(string uid)
        {
            await SetRole(uid, "admin");
            return new UserRole { Role = "admin" };
        }

        public async Task<UserRole> PromoteUserToHost(string uid)
        {
            await SetRole(uid, "host");
            return new UserRole { Role = "host" };
        }

        public async Task ClearUserRole(string uid)
        {
            await _firebaseAdminService.ClearUserRoleAsync(uid);
        }

        public async Task<UserRole> GetUserRole(string uid)
        {
            var role = await _firebaseAdminService.GetUserRoleAsync(uid);
            return new UserRole { Role = role };
        }

        private async Task SetRole(string uid, string role)
        {
            await _firebaseAdminService.SetUserRoleAsync(uid, role);
        }
    }
}
