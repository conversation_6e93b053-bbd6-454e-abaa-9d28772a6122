namespace Savvy.AppBackend.Data.CosmosDb
{
    public static class CosmosDbContainers
    {
        public const string StreamContainerId = "streams";

        public const string UserGameDetailsContainerId = "userGameDetails";

        public const string UsersContainerId = "users";

        public static IReadOnlyList<(string, string)> GetContainers(string databaseId) {
            return new List<(string, string)> {
                (databaseId, StreamContainerId),
                (databaseId, UserGameDetailsContainerId),
                (databaseId, UsersContainerId),
            };
        }
    }
}
