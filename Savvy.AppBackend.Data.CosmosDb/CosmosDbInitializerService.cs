using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Microsoft.Azure.Cosmos;

namespace Savvy.AppBackend.Data.CosmosDb;

public class CosmosDbInitializerService : IHostedService
{
    private readonly CosmosDbOptions _options;
    private readonly ILogger<CosmosDbInitializerService> _logger;

    public CosmosDbInitializerService(
        IOptions<CosmosDbOptions> options,
        ILogger<CosmosDbInitializerService> logger)
    {
        _options = options.Value;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting Cosmos DB initialization...");

        try
        {
            var connectionString = _options.ConnectionString;
            var _databaseId = _options.DatabaseId;
            
            _logger.LogInformation("Checking if Cosmos DB database '{DatabaseId}' exists or needs creation...", _databaseId);
            
            var client = new CosmosClient(connectionString);
            var databaseResponse = await client.CreateDatabaseIfNotExistsAsync(_databaseId);
            var database = databaseResponse.Database;

            _logger.LogInformation("Database '{DatabaseId}' is ready.", _databaseId);

            foreach (var (_, containerId) in CosmosDbContainers.GetContainers(_databaseId))
            {
                try
                {
                    _logger.LogInformation("Checking if container '{ContainerId}' exists or needs creation...", containerId);

                    var containerResponse = await database.CreateContainerIfNotExistsAsync(containerId, "/pk");

                    if (containerResponse.StatusCode == System.Net.HttpStatusCode.Created)
                    {
                        _logger.LogInformation("Container '{ContainerId}' was created successfully.", containerId);
                    }
                    else if (containerResponse.StatusCode == System.Net.HttpStatusCode.OK)
                    {
                        _logger.LogInformation("Container '{ContainerId}' already exists.", containerId);
                    }
                    else
                    {
                        _logger.LogWarning("Unexpected status code {StatusCode} for container '{ContainerId}'.", containerResponse.StatusCode, containerId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating or accessing container '{ContainerId}'", containerId);
                    throw;
                }
            }

            _logger.LogInformation("All Cosmos DB containers initialized successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Cosmos DB containers.");
            throw;
        }
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}