using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Cosmos.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;

namespace Savvy.AppBackend.Data.CosmosDb;

public class CosmosDbRepository : ICosmosDbRepository
{
    private readonly ILogger<CosmosDbRepository> _logger;
    private readonly CosmosClient _cosmosClient;
    private readonly string _databaseId;

    public CosmosDbRepository(ILogger<CosmosDbRepository> logger, IOptions<CosmosDbOptions> cosmosDbOptions, CosmosClient cosmosClient)
    {
        _logger = logger;
        
        var  cosmosDbOptionsValue= cosmosDbOptions.Value;
        _databaseId = cosmosDbOptionsValue.DatabaseId;
        _cosmosClient = cosmosClient;
    }

    public async Task<TCosmosDocument> UpsertItemAsync<TCosmosDocument>(string containerId, TCosmosDocument document)  where TCosmosDocument : CosmosDocument
    {
        try
        {
            var container = _cosmosClient.GetContainer(_databaseId, containerId);
            var itemResponse = await container.UpsertItemAsync(document, new PartitionKey(document.PartitionKey));
            _logger.LogInformation($"Item {document.Id} is upserted, RequestCharge: {itemResponse.RequestCharge} RUs");
            
            return itemResponse.Resource;
        }
        catch(CosmosException exception)
        {
            _logger.LogError($"Item {document.Id} is not upserted, Error: {exception.Message}");
            throw;
        }
    }

    public async Task<TCosmosDocument> ReadItemAsync<TCosmosDocument>(string containerId, string id, string partitionKey) where TCosmosDocument : CosmosDocument
    {
        try
        {
            var container = _cosmosClient.GetContainer(_databaseId, containerId);
            var itemResponse = await container.ReadItemAsync<TCosmosDocument>(id, new PartitionKey(partitionKey));
            
            _logger.LogInformation($"Item {id} is read, RequestCharge: {itemResponse.RequestCharge} RUs");
            
            return itemResponse.Resource;
        }
        catch (CosmosException exception)
        {
            _logger.LogError($"Item {id} is not found, Error: {exception.Message}");
            throw;
        }
    }

    public async Task<List<TCosmosDocument>> ListItemsAsync<TCosmosDocument>(string containerId,
        Func<IOrderedQueryable<TCosmosDocument>, IQueryable<TCosmosDocument>> filter)
        where TCosmosDocument : CosmosDocument
    {
        var result = new List<TCosmosDocument>();

        try
        {
            var container = _cosmosClient.GetContainer(_databaseId, containerId);
            var query = container.GetItemLinqQueryable<TCosmosDocument>();
            var queryDefinition = filter.Invoke(query).ToQueryDefinition();

            using var iterator = container.GetItemQueryStreamIterator(queryDefinition);
            while (iterator.HasMoreResults)
            {
                using var response = await iterator.ReadNextAsync();
                if (response.IsSuccessStatusCode)
                {
                    var streamResponse = FromStream<dynamic>(response.Content);
                    var documents = streamResponse?.Documents.ToObject<List<TCosmosDocument>>();
                    result.AddRange(documents);
                }
                else
                {
                    _logger.LogError($"Couldn't read response {response.StatusCode} {response.ErrorMessage}");
                }
            }
        }
        catch (CosmosException exception)
        {
            _logger.LogError($"Items are not found, Error: {exception.Message}");
            throw;
        }

        return result;
    }

    public async Task<List<TCosmosDocument>> ListItemsWithSqlAsyns<TCosmosDocument>(string containerId,
        QueryDefinition query)
        where TCosmosDocument : CosmosDocument
    {
        var result = new List<TCosmosDocument>();

        try
        {
            var container = _cosmosClient.GetContainer(_databaseId, containerId);
            using var iterator = container.GetItemQueryIterator<TCosmosDocument>(query);

            while (iterator.HasMoreResults)
            {
                var response = await iterator.ReadNextAsync();
                foreach (var item in response)
                {
                        result.AddRange(item);
                }
            }
        }
        catch (CosmosException exception)
        {
            _logger.LogError($"Error during executing SQL request, Error: {exception.Message}");
            throw;
        }

        return result;
    }

    public async Task<T> CallStoredProcedure<T>(string containerId, string procedureName, string partition, params object[] parameters)
    {
        var container = _cosmosClient.GetContainer(_databaseId, containerId);
        var result = await container.Scripts.ExecuteStoredProcedureAsync<T>(procedureName, new PartitionKey(partition), parameters);
        return result;
    }

    private static T? FromStream<T>(Stream stream)
    {
        using (stream)
        {
            using var streamReader = new StreamReader(stream);
            using var jsonTextReader = new JsonTextReader(streamReader);
            var jsonSerializer = new JsonSerializer();

            return jsonSerializer.Deserialize<T>(jsonTextReader);
        }
    }
}