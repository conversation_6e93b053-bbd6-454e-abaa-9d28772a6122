namespace Savvy.AppBackend.Data.CosmosDb.Documents;

public class StreamDocument : CosmosDocument
{
    public required string HostId { get; set; }
    public required string Title { get; set; }
    public required string HostName { get; set; } = string.Empty;
    public required string ImageUrl { get; set; } = string.Empty;
    public DateTimeOffset StartDate { get; set; }
    public StreamStatus Status { get; set; }
    public StreamType Type { get; set; }
    public int Prize { get; set; } = 0;
}