using Microsoft.Azure.Cosmos;

namespace Savvy.AppBackend.Data.CosmosDb;

public interface ICosmosDbRepository
{
    Task<TCosmosDocument> UpsertItemAsync<TCosmosDocument>(string containerId, TCosmosDocument document) 
        where TCosmosDocument : CosmosDocument;
    Task<TCosmosDocument> ReadItemAsync<TCosmosDocument>(string containerId, string id, string partitionKey)
        where TCosmosDocument : CosmosDocument;

    Task<List<TCosmosDocument>> ListItemsAsync<TCosmosDocument>(string containerId,
        Func<IOrderedQueryable<TCosmosDocument>, IQueryable<TCosmosDocument>> filter)
        where TCosmosDocument : CosmosDocument;

    Task<T> CallStoredProcedure<T>(string containerId, string procedureName, string partition, params object[] parameters);

    Task<List<TCosmosDocument>> ListItemsWithSqlAsyns<TCosmosDocument>(string containerId,
        QueryDefinition query)
        where TCosmosDocument : CosmosDocument;
}