<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
      <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.51.0" />
      <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.5" />
      <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.5" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    </ItemGroup>

</Project>
