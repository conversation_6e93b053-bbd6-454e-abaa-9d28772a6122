using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Options;

namespace Savvy.AppBackend.Data.CosmosDb;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCosmosDb(this IServiceCollection services, SecretClient kvClient)
    {
        KeyVaultSecret connectionStringSecret = kvClient.GetSecret("cosmosdb-connection-string");
        KeyVaultSecret databaseIdSecret = kvClient.GetSecret("cosmosdb-database-id");

        var options = new CosmosDbOptions
        {
            ConnectionString = connectionStringSecret.Value,
            DatabaseId = databaseIdSecret.Value
        };

        services.AddSingleton(Options.Create(options));

        services.AddSingleton(services =>
        {
            var client = CosmosClient.CreateAndInitializeAsync(
                options.ConnectionString,
                CosmosDbContainers.GetContainers(options.DatabaseId)
            ).GetAwaiter().GetResult();

            return client;
        });

        services.AddSingleton<ICosmosDbRepository, CosmosDbRepository>();
        services.AddHostedService<CosmosDbInitializerService>();

        return services;
    }
}