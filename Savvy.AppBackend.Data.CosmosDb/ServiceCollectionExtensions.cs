using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Savvy.AppBackend.Data.CosmosDb;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCosmosDb(this IServiceCollection services, IConfiguration configuration)
    {
        var options = configuration.GetSection(nameof(CosmosDbOptions));

        var connectionString = options.GetSection(nameof(CosmosDbOptions.ConnectionString)).Value;
        var databaseId = options.GetSection(nameof(CosmosDbOptions.DatabaseId)).Value;

        services.Configure<CosmosDbOptions>(options);
        services.AddSingleton(services => {
            var client = CosmosClient.CreateAndInitializeAsync(connectionString, CosmosDbContainers.GetContainers(databaseId)).GetAwaiter().GetResult();
            return client;
        });
        services.AddSingleton<ICosmosDbRepository, CosmosDbRepository>();        
        return services;
    }
}