namespace Savvy.AppBackend.Domain.Models;

public enum HealthStatus
{
    Healthy,
    Degraded,
    Unhealthy
}

public class ServiceHealthCheck
{
    public string ServiceName { get; set; } = string.Empty;
    public HealthStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public DateTime CheckedAt { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

public class OverallHealthCheck
{
    public HealthStatus Status { get; set; }
    public string Description { get; set; } = string.Empty;
    public DateTime CheckedAt { get; set; }
    public TimeSpan TotalResponseTime { get; set; }
    public List<ServiceHealthCheck> Services { get; set; } = new();
}
