using Savvy.AppBackend.Domain.Models;

namespace Savvy.AppBackend.Domain.Services
{
    public interface IDateTimeService
    {
        /// <summary>
        /// Gets start and end of the week in which the games are held.
        /// </summary>
        TimeRange GetStartAndEndOfTheGameWeek();

        /// <summary>
        /// Gets start and end of the current week. (Includes 7 days)
        /// </summary>
        TimeRange GetStartAndEndOfTheFullWeek();

        /// <summary>
        /// Gets start and end of the current month. (Includes 28 - 31 days)
        /// </summary>
        TimeRange GetStartAndEndOfCurrentMonth();
    }
}
