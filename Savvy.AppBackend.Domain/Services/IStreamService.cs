using Savvy.AppBackend.Domain.Models;

namespace Savvy.AppBackend.Domain.Services;

public interface IStreamService
{
    Task<IEnumerable<StreamDetail>> GetScheduledStreamsAsync();

    Task<StreamDetail> CreateStreamAsync(StreamDetail streamDetail);

    Task UpdateStreamAsync(string id, string hostId, StreamDetail streamDetail);

    Task UpdateStreamStatus(string id, string hostId, StreamStatus streamStatus);

    Task<IEnumerable<StreamDetail>> GetWeeklyStreams();
}