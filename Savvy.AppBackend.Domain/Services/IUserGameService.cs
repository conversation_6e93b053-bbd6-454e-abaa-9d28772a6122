using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.Leaderboard;

namespace Savvy.AppBackend.Domain.Services
{
    public interface IUserGameService
    {
        Task<UserGameDetails> AddUserGameDetailsAsync(UserGameDetails userStreamDetails);

        Task<IEnumerable<UserGameDetails>> GetUserGameDetailsByStreamsAndUser(string[] streamIds, string userId);

        Task<IEnumerable<UserGameDetails>> GetUserGameDetailsByStreams(string[] streamIds);

        /// <summary>
        /// Gets total usesr's score by all time.
        /// <example>
        /// Stored procedure:
        /// <code>
        /// var query = {
        ///     query: "SELECT c.UserId, SUM(c.GameScore) as 'TotalScore' FROM c WHERE c.pk = @partitionKey GROUP BY c.UserId",
        ///     parameters: [{ name: "@partitionKey", value: partitionKey }]
        /// };
        ///
        /// var isAccepted = collection.queryDocuments(
        ///     collection.getSelfLink(),
        ///     query,
        ///     function(err, docs, options) {
        ///        if (err)throw err;
        ///
        ///         //Sorting by TotalScore
        ///         docs.sort((a, b) => b.TotalScore - a.TotalScore);
        ///
        ///         // skip \ limit logic
        ///         var result = docs.slice(skip, skip + limit)
        ///    
        ///          response.setBody(Object.values(result));
        /// });
        /// </code>
        /// </example>
        /// </summary>
        /// <param name="limit"></param>
        /// <param name="skip"></param>
        /// <returns></returns>
        Task<IEnumerable<UserScoreDetails>> GetAllTimeUserGameDetails(int limit, int skip);

        Task<IEnumerable<UserGameDetails>> GetWeeklyUserGameResults();

        Task<IEnumerable<UserGameDetails>> GetMonthlyUserGameResults();
    }
}
