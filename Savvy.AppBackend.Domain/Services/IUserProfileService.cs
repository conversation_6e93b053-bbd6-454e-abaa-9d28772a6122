using Savvy.AppBackend.Domain.Models.UserProfile;

namespace Savvy.AppBackend.Domain.Services;

public interface IUserProfileService
{
    Task<UserProfile> CreateUserProfileAsync(UserProfile userProfile);
    Task<UserProfile> GetUserProfileByIdAsync(string userId);
    Task<UserProfile> GetUserProfileByUsernameAsync(string userName);
    Task<UserProfile> UpdateSparksNumber(int sparksToUpdate, string userId);

    Task<IEnumerable<UserProfileShortInfo>> GetListOfUsersByIdAsync(string[] userIds);
}