using System.Diagnostics;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Savvy.AppBackend.Infrastructure.HealthChecks;

public class ApplicationInsightsHealthCheck : IHealthCheck
{
    private readonly TelemetryClient _telemetryClient;
    private readonly ILogger<ApplicationInsightsHealthCheck> _logger;

    public ApplicationInsightsHealthCheck(
        TelemetryClient telemetryClient,
        ILogger<ApplicationInsightsHealthCheck> logger)
    {
        _telemetryClient = telemetryClient;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Send a test telemetry event to verify Application Insights connection
            var testEvent = new EventTelemetry("HealthCheck")
            {
                Timestamp = DateTimeOffset.UtcNow
            };
            testEvent.Properties["Source"] = "HealthCheckService";
            testEvent.Properties["CheckId"] = Guid.NewGuid().ToString();

            _telemetryClient.TrackEvent(testEvent);
            
            // Flush to ensure the telemetry is sent
            _telemetryClient.Flush();
            
            // Wait a short time for the flush to complete
            await Task.Delay(100, cancellationToken);
            
            stopwatch.Stop();

            var data = new Dictionary<string, object>
            {
                ["InstrumentationKey"] = _telemetryClient.InstrumentationKey ?? "Not configured",
                ["IsEnabled"] = !_telemetryClient.IsEnabled() ? "Disabled" : "Enabled",
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Healthy("Application Insights connection is healthy", data);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Application Insights health check failed: {Message}", ex.Message);
            
            var data = new Dictionary<string, object>
            {
                ["Error"] = ex.Message,
                ["ExceptionType"] = ex.GetType().Name,
                ["Note"] = "Application Insights failures are often non-blocking",
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            // Application Insights failures are typically non-critical, so return Degraded instead of Unhealthy
            return HealthCheckResult.Degraded("Application Insights may have issues", ex, data);
        }
    }
}
