using System.Diagnostics;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Savvy.AppBackend.Data.CosmosDb;

namespace Savvy.AppBackend.Infrastructure.HealthChecks;

public class CosmosDbHealthCheck : IHealthCheck
{
    private readonly CosmosClient _cosmosClient;
    private readonly CosmosDbOptions _cosmosDbOptions;
    private readonly ILogger<CosmosDbHealthCheck> _logger;

    public CosmosDbHealthCheck(
        CosmosClient cosmosClient,
        IOptions<CosmosDbOptions> cosmosDbOptions,
        ILogger<CosmosDbHealthCheck> logger)
    {
        _cosmosClient = cosmosClient;
        _cosmosDbOptions = cosmosDbOptions.Value;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Try to read account properties to verify connection
            var accountProperties = await _cosmosClient.ReadAccountAsync();
            
            // Try to get database to verify it exists
            var database = _cosmosClient.GetDatabase(_cosmosDbOptions.DatabaseId);
            var databaseResponse = await database.ReadAsync(cancellationToken: cancellationToken);
            
            stopwatch.Stop();

            var data = new Dictionary<string, object>
            {
                ["DatabaseId"] = _cosmosDbOptions.DatabaseId,
                ["AccountId"] = accountProperties.Id,
                ["ReadableRegions"] = accountProperties.ReadableRegions?.Count ?? 0,
                ["WritableRegions"] = accountProperties.WritableRegions?.Count ?? 0,
                ["ConsistencyLevel"] = accountProperties.Consistency?.DefaultConsistencyLevel.ToString() ?? "Unknown",
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Healthy("CosmosDB connection is healthy", data);
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
        {
            stopwatch.Stop();
            _logger.LogWarning("CosmosDB health check failed due to rate limiting: {Message}", ex.Message);
            
            var data = new Dictionary<string, object>
            {
                ["Error"] = "Rate limited",
                ["RetryAfter"] = ex.RetryAfter?.TotalSeconds ?? 0,
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Degraded("CosmosDB is experiencing rate limiting", ex, data);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "CosmosDB health check failed: {Message}", ex.Message);
            
            var data = new Dictionary<string, object>
            {
                ["Error"] = ex.Message,
                ["ExceptionType"] = ex.GetType().Name,
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Unhealthy("CosmosDB connection failed", ex, data);
        }
    }
}
