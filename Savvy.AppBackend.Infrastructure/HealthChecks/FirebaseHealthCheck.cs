using System.Diagnostics;
using FirebaseAdmin.Auth;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace Savvy.AppBackend.Infrastructure.HealthChecks;

public class FirebaseHealthCheck : IHealthCheck
{
    private readonly FirebaseAuth _firebaseAuth;
    private readonly ILogger<FirebaseHealthCheck> _logger;

    public FirebaseHealthCheck(FirebaseAuth firebaseAuth, ILogger<FirebaseHealthCheck> logger)
    {
        _firebaseAuth = firebaseAuth;
        _logger = logger;
    }

    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            // Try to get a non-existent user to verify Firebase connection
            // This will throw a specific exception if the user doesn't exist, but confirms connection
            try
            {
                await _firebaseAuth.GetUserAsync("health-check-non-existent-user");
            }
            catch (FirebaseAuthException ex) when (ex.ErrorCode == ErrorCode.UserNotFound)
            {
                // This is expected - it means Firebase is responding correctly
            }
            
            stopwatch.Stop();

            var data = new Dictionary<string, object>
            {
                ["ProjectId"] = FirebaseAdmin.FirebaseApp.DefaultInstance.Options.ProjectId ?? "Unknown",
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Healthy("Firebase connection is healthy", data);
        }
        catch (FirebaseAuthException ex) when (ex.ErrorCode != AuthErrorCode.UserNotFound)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Firebase health check failed: {Message}", ex.Message);
            
            var data = new Dictionary<string, object>
            {
                ["Error"] = ex.Message,
                ["ErrorCode"] = ex.ErrorCode.ToString(),
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Unhealthy("Firebase connection failed", ex, data);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Firebase health check failed: {Message}", ex.Message);
            
            var data = new Dictionary<string, object>
            {
                ["Error"] = ex.Message,
                ["ExceptionType"] = ex.GetType().Name,
                ["ResponseTimeMs"] = stopwatch.Elapsed.TotalMilliseconds
            };

            return HealthCheckResult.Unhealthy("Firebase connection failed", ex, data);
        }
    }
}
