using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Savvy.AppBackend.Infrastructure.HealthChecks;

public static class HealthCheckResponseWriter
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = true
    };

    public static async Task WriteResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var response = new
        {
            AppVersion = Environment.GetEnvironmentVariable("APP_VERSION"),
            Status = healthReport.Status.ToString(),
            Description = GetOverallDescription(healthReport),
            CheckedAt = DateTime.UtcNow,
            TotalResponseTimeMs = healthReport.TotalDuration.TotalMilliseconds,
            Services = healthReport.Entries.Select(entry => new
            {
                ServiceName = entry.Key,
                Status = entry.Value.Status.ToString(),
                Description = entry.Value.Description ?? $"{entry.Key} health check",
                ResponseTimeMs = entry.Value.Duration.TotalMilliseconds,
                CheckedAt = DateTime.UtcNow,
                Data = entry.Value.Data.Count > 0 ? entry.Value.Data : null,
                Exception = entry.Value.Exception?.Message
            }).ToList()
        };

        var json = JsonSerializer.Serialize(response, JsonOptions);
        await context.Response.WriteAsync(json);
    }

    public static async Task WriteSimpleResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var response = new
        {
            Status = healthReport.Status.ToString(),
            CheckedAt = DateTime.UtcNow
        };

        var json = JsonSerializer.Serialize(response, JsonOptions);
        await context.Response.WriteAsync(json);
    }

    public static async Task WriteDetailedServiceResponse(HttpContext context, HealthReport healthReport, string serviceName)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        if (!healthReport.Entries.TryGetValue(serviceName, out var entry))
        {
            context.Response.StatusCode = 404;
            var notFoundResponse = new
            {
                Error = $"Health check for service '{serviceName}' not found",
                AvailableServices = healthReport.Entries.Keys.ToList()
            };
            var notFoundJson = JsonSerializer.Serialize(notFoundResponse, JsonOptions);
            await context.Response.WriteAsync(notFoundJson);
            return;
        }

        var response = new
        {
            ServiceName = serviceName,
            Status = entry.Status.ToString(),
            Description = entry.Description ?? $"{serviceName} health check",
            ResponseTimeMs = entry.Duration.TotalMilliseconds,
            CheckedAt = DateTime.UtcNow,
            Data = entry.Data.Count > 0 ? entry.Data : null,
            Exception = entry.Exception?.Message
        };

        var json = JsonSerializer.Serialize(response, JsonOptions);
        await context.Response.WriteAsync(json);
    }

    private static string GetOverallDescription(HealthReport healthReport)
    {
        var totalServices = healthReport.Entries.Count;
        var healthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Healthy);
        var degradedCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Degraded);
        var unhealthyCount = healthReport.Entries.Count(e => e.Value.Status == HealthStatus.Unhealthy);

        return healthReport.Status switch
        {
            HealthStatus.Healthy => $"All {totalServices} services are healthy",
            HealthStatus.Degraded => $"{healthyCount} healthy, {degradedCount} degraded, {unhealthyCount} unhealthy services",
            HealthStatus.Unhealthy => $"{unhealthyCount} critical service(s) are unhealthy",
            _ => "Unknown status"
        };
    }
}
