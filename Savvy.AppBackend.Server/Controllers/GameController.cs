using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Middleware;

namespace Savvy.AppBackend.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class GameController : ControllerBase
    {
        public readonly IGameService _homeService;

        public GameController(IGameService homeService)
        {
            _homeService = homeService;
        }

        [HttpGet("weekly-games")]
        public async Task<IActionResult> GetWeeklyUserGameDetails([FromJwt] string userId)
        {
            var homeDetails = await _homeService.GetWeeklyGamesAndDetailsAsync(userId);
            return Ok(homeDetails);
        }
    }
}
