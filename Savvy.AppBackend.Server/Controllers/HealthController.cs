using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Models;

namespace Savvy.AppBackend.Server.Controllers;

[ApiController]
[Route("[controller]")]
public class HealthController : ControllerBase
{
    private readonly IHealthCheckService _healthCheckService;
    private readonly ILogger<HealthController> _logger;

    public HealthController(IHealthCheckService healthCheckService, ILogger<HealthController> logger)
    {
        _healthCheckService = healthCheckService;
        _logger = logger;
    }

    /// <summary>
    /// Gets the overall health status of all services
    /// </summary>
    /// <returns>Overall health check result including all service statuses</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<ActionResult<OverallHealthCheckResponse>> GetOverallHealth()
    {
        try
        {
            var healthCheck = await _healthCheckService.GetOverallHealthAsync();
            
            var statusCode = healthCheck.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200, // Still return 200 for degraded services
                HealthStatus.Unhealthy => 503, // Service Unavailable
                _ => 500
            };

            _logger.LogInformation("Health check completed with status: {Status}", healthCheck.Status);

            return StatusCode(statusCode, OverallHealthCheckResponse.FromOverallHealthCheck(healthCheck));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed with exception: {Message}", ex.Message);
            
            return StatusCode(500, OverallHealthCheckResponse.FromOverallHealthCheck(new OverallHealthCheck
            {
                Status = HealthStatus.Unhealthy,
                Description = "Health check service failed",
                CheckedAt = DateTime.UtcNow,
                TotalResponseTime = TimeSpan.Zero,
                Services = new List<ServiceHealthCheck>()
            }));
        }
    }

    /// <summary>
    /// Gets the health status of CosmosDB
    /// </summary>
    /// <returns>CosmosDB health check result</returns>
    [HttpGet("cosmosdb")]
    [Authorize(Policy = "AdminOnly")]
    public async Task<ActionResult<HealthCheckResponse>> GetCosmosDbHealth()
    {
        try
        {
            var healthCheck = await _healthCheckService.CheckCosmosDbHealthAsync();
            
            var statusCode = healthCheck.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200,
                HealthStatus.Unhealthy => 503,
                _ => 500
            };

            return StatusCode(statusCode, HealthCheckResponse.FromServiceHealthCheck(healthCheck));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "CosmosDB health check failed: {Message}", ex.Message);
            
            return StatusCode(500, HealthCheckResponse.FromServiceHealthCheck(new ServiceHealthCheck
            {
                ServiceName = "CosmosDB",
                Status = HealthStatus.Unhealthy,
                Description = "Health check failed with exception",
                ResponseTime = TimeSpan.Zero,
                CheckedAt = DateTime.UtcNow,
                Data = new Dictionary<string, object> { ["Error"] = ex.Message }
            }));
        }
    }

    /// <summary>
    /// Gets the health status of Firebase
    /// </summary>
    /// <returns>Firebase health check result</returns>
    [HttpGet("firebase")]
    [Authorize(Policy = "AdminOnly")]
    public async Task<ActionResult<HealthCheckResponse>> GetFirebaseHealth()
    {
        try
        {
            var healthCheck = await _healthCheckService.CheckFirebaseHealthAsync();
            
            var statusCode = healthCheck.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200,
                HealthStatus.Unhealthy => 503,
                _ => 500
            };

            return StatusCode(statusCode, HealthCheckResponse.FromServiceHealthCheck(healthCheck));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Firebase health check failed: {Message}", ex.Message);

            return StatusCode(500, HealthCheckResponse.FromServiceHealthCheck(new ServiceHealthCheck
            {
                ServiceName = "Firebase",
                Status = HealthStatus.Unhealthy,
                Description = "Health check failed with exception",
                ResponseTime = TimeSpan.Zero,
                CheckedAt = DateTime.UtcNow,
                Data = new Dictionary<string, object> { ["Error"] = ex.Message }
            }));
        }
    }

    /// <summary>
    /// Gets the health status of Application Insights
    /// </summary>
    /// <returns>Application Insights health check result</returns>
    [HttpGet("applicationinsights")]
    [Authorize(Policy = "AdminOnly")]
    public async Task<ActionResult<HealthCheckResponse>> GetApplicationInsightsHealth()
    {
        try
        {
            var healthCheck = await _healthCheckService.CheckApplicationInsightsHealthAsync();
            
            var statusCode = healthCheck.Status switch
            {
                HealthStatus.Healthy => 200,
                HealthStatus.Degraded => 200,
                HealthStatus.Unhealthy => 503,
                _ => 500
            };

            return StatusCode(statusCode, HealthCheckResponse.FromServiceHealthCheck(healthCheck));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Application Insights health check failed: {Message}", ex.Message);

            return StatusCode(500, HealthCheckResponse.FromServiceHealthCheck(new ServiceHealthCheck
            {
                ServiceName = "Application Insights",
                Status = HealthStatus.Unhealthy,
                Description = "Health check failed with exception",
                ResponseTime = TimeSpan.Zero,
                CheckedAt = DateTime.UtcNow,
                Data = new Dictionary<string, object> { ["Error"] = ex.Message }
            }));
        }
    }

    /// <summary>
    /// Simple liveness probe endpoint
    /// </summary>
    /// <returns>Simple OK response</returns>
    [HttpGet("live")]
    [AllowAnonymous]
    public IActionResult GetLiveness()
    {
        return Ok(new { status = "alive", timestamp = DateTime.UtcNow });
    }

    /// <summary>
    /// Simple readiness probe endpoint
    /// </summary>
    /// <returns>Simple ready response</returns>
    [HttpGet("ready")]
    [AllowAnonymous]
    public IActionResult GetReadiness()
    {
        // You can add basic readiness checks here if needed
        return Ok(new { status = "ready", timestamp = DateTime.UtcNow });
    }
}
