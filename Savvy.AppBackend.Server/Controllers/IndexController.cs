using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Savvy.AppBackend.Server.Controllers;

[ApiController]
public class IndexController : ControllerBase
{
    /// <summary>
    /// Root endpoint that returns basic API information
    /// </summary>
    /// <returns>API information and status</returns>
    [HttpGet("/")]
    [AllowAnonymous]
    public IActionResult Index()
    {
        var response = new
        {
            name = "Savvy App Backend API",
            version = "1.0.0",
            status = "running",
            timestamp = DateTime.UtcNow,
            environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production",
            endpoints = new
            {
                health = "/health",
                swagger = "/swagger",
                openapi = "/openapi/v1.json"
            }
        };

        return Ok(response);
    }

    /// <summary>
    /// Alternative root endpoint for ping/status checks
    /// </summary>
    /// <returns>Simple pong response</returns>
    [HttpGet("/ping")]
    [AllowAnonymous]
    public IActionResult Ping()
    {
        return Ok(new { message = "pong", timestamp = DateTime.UtcNow });
    }
}
