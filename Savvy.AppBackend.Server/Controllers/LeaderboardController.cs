using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Models;
using System.ComponentModel;

namespace Savvy.AppBackend.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    [Produces("application/json")]
    public class LeaderboardController : ControllerBase
    {
        private readonly ILeaderboardService _leaderboardService;

        public LeaderboardController(ILeaderboardService leaderboardService)
        {
            _leaderboardService = leaderboardService;
        }

        [HttpGet]
        [ProducesResponseType<IEnumerable<LeaderboardUserResponse>>(StatusCodes.Status200OK)]
        [EndpointDescription("EndpointDescription: Gets list of users")]
        public async Task<IActionResult> GetLeaderboardAsync([FromQuery] [Description("Param description")] GetLeaderboardRequest getLeaderboardRequest)
        {
            var leaderboard = await _leaderboardService.GetLeaderboardAsync(getLeaderboardRequest.TimeFilter, getLeaderboardRequest.Limit, getLeaderboardRequest.Skip);
            return Ok(leaderboard.Select(e => new LeaderboardUserResponse
            {
                Rank = e.Rank,
                Username = e.Username,
                ImageUrl = e.ImageUrl,
                Score = e.TotalScore
            }));
        }
    }
}
