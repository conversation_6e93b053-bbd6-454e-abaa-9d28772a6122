using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models.PowerupModels;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Middleware;
using Savvy.AppBackend.Server.Models.Powerups;

namespace Savvy.AppBackend.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class PowerupsController : ControllerBase
    {
        private readonly IPowerupService _powerupService;

        public PowerupsController(IPowerupService powerupService)
        {
            _powerupService = powerupService;
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        public async Task<IActionResult> AddPowerups([FromBody] IEnumerable<AddPowerupsRequest> powerups, [FromJwt] string userId)
        {
            var powerupsShortInfo = powerups.Adapt<IEnumerable<PowerupShortInfo>>();
            var addedPowerups = await _powerupService.UpdatePowerupsNumber(powerupsShortInfo, userId);

            return Created(string.Empty, addedPowerups);
        }

        [HttpGet]
        [ProducesResponseType<IEnumerable<PowerupShortInfo>>(StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUsersPowerups([FromJwt] string userId)
        {
            var powerups = await _powerupService.GetUsersPowerups(userId);
            
            return Ok(powerups);
        }
    }
}
