using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Middleware;
using Savvy.AppBackend.Server.Models;

namespace Savvy.AppBackend.Server.Controllers;

[Authorize]
[ApiController]
[Route("api/v1/[controller]")]
public class StreamsController : ControllerBase
{
    private readonly IStreamService _streamService;

    public StreamsController(IStreamService streamService)
    {
        _streamService = streamService;
    }

    [HttpGet("scheduled")]
    public async Task<IActionResult> GetScheduledStreamsAsync()
    {
        var scheduledStreams = await _streamService.GetScheduledStreamsAsync();
        return Ok(scheduledStreams);
    }

    [Authorize(Policy = "AdminOnly")]
    [HttpPost]
    public async Task<IActionResult> CreateStreamDetailAsync(CreateStreamDetailRequest createStreamDetailRequest)
    {
        var streamDetail = createStreamDetailRequest.Adapt<StreamDetail>();
        var newStreamDetail = await _streamService.CreateStreamAsync(streamDetail);

        return Created(string.Empty, newStreamDetail);
    }

    [Authorize(Policy = "AdminOnly")]
    [HttpPut("{id}/{hostId}")]
    public async Task<IActionResult> UpdateStreamDetailAsync(string id, string hostId, UpdateStreamDetailRequest updateStreamDetailRequest)
    {
        var streamDetail = updateStreamDetailRequest.Adapt<StreamDetail>();
        await _streamService.UpdateStreamAsync(id, hostId, streamDetail);

        return NoContent();
    }

    [Authorize(Policy = "HostOnly")]
    [HttpPut("{id}/status")]
    public async Task<IActionResult> UpdateStreamStatusAsync(string id, StreamStatus streamStatus, [FromJwt] string userId)
    {
        await _streamService.UpdateStreamStatus(id, userId, streamStatus);

        return NoContent();
    }
}