using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Middleware;
using Savvy.AppBackend.Server.Models.UserGameModels;

namespace Savvy.AppBackend.Server.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    public class UserGameController : ControllerBase
    {
        private readonly IUserGameService _userGameService;

        public UserGameController(IUserGameService userGameService)
        {
            _userGameService = userGameService;
        }

        [HttpPost(Name = "AddUserGameDetailsAsync")]
        public async Task<IActionResult> AddUserGameDetailsAsync([FromBody] AddUserGameDetailsRequest request, [FromJwt] string userId)
        {
            var userGameDetails = request.Adapt<UserGameDetails>();
            userGameDetails.UserId = userId;
            await _userGameService.AddUserGameDetailsAsync(userGameDetails);
            return Created();
        }
    }
}
