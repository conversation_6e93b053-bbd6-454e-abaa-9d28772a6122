using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Middleware;
using Savvy.AppBackend.Server.Models;

namespace Savvy.AppBackend.Server.Controllers;

[Authorize]
[ApiController]
[Route("api/v1/[controller]")]
public class UserProfilesController : ControllerBase
{
    private readonly IUserProfileService _userProfileService;

    public UserProfilesController(IUserProfileService userProfileService)
    {
        _userProfileService = userProfileService;
    }

    [HttpPost(Name = "CreateUserProfile")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    public async Task<IActionResult> CreateUserProfileAsync([FromBody] CreateUserProfileRequest createUserProfileRequest, [FromJwt] string userId)
    {
        var newUserProfile = await _userProfileService.CreateUserProfileAsync(new UserProfile
        {
            Id = userId,
            Username = createUserProfileRequest.Username,
            Email = createUserProfileRequest.Email,
            ImageUrl = createUserProfileRequest.ImageUrl
        });

        return Created(string.Empty, newUserProfile);
    }

    [HttpGet("username/{username}", Name = "GetUserProfileByUsername")]
    [ProducesResponseType<GetUserProfileResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetUserProfileByUsernameAsync(string username)
    {
        var userProfile = await _userProfileService.GetUserProfileByUsernameAsync(username);
        return Ok(userProfile.Adapt<GetUserProfileResponse>());
    }

    [HttpGet("users")]
    [ProducesResponseType<IEnumerable<UserProfileShortInfo>>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetListOfUsersByIdAsync([FromQuery]string[] userIds)
    {
        var users = await _userProfileService.GetListOfUsersByIdAsync(userIds);
        return Ok(users);
    }

    [HttpGet(Name = "GetUserProfileById")]
    [ProducesResponseType<GetUserProfileResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> GetUserProfileByIdAsync([FromJwt] string userId)
    {
        var userProfile = await _userProfileService.GetUserProfileByIdAsync(userId);
        return Ok(userProfile.Adapt<GetUserProfileResponse>());
    }

    [HttpPatch("sparks")]
    [ProducesResponseType<GetUserProfileResponse>(StatusCodes.Status200OK)]
    public async Task<IActionResult> UpdateSparksNumber([FromQuery] int sparksToUpdate, [FromJwt] string userId)
    {
        var userProfile = await _userProfileService.UpdateSparksNumber(sparksToUpdate, userId);
        return Ok(userProfile.Adapt<GetUserProfileResponse>());
    }
}

