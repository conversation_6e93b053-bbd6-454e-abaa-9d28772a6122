using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Server.Extensions;

namespace Savvy.AppBackend.Server.Controllers
{
    [Authorize(Policy = "AdminOnly")]
    [Route("api/v1/[controller]")]
    [ApiController]
    public class UserRoleController : ControllerBase
    {
        private readonly IUserRoleService _userRoleService;
        private readonly ILogger<UserRoleController> _logger;

        public UserRoleController(IUserRoleService userRoleService, ILogger<UserRoleController> logger)
        {
            _userRoleService = userRoleService;
            _logger = logger;
        }

        [HttpGet("{uid}/getUserClaims")]
        public async Task<IActionResult> GetUserClaims(string uid)
        {
            var userRole = await _userRoleService.GetUserRole(uid);
            return Ok(userRole.Adapt<UserRole>());
        }

        [HttpPost("{uid}/prmoteUserToHost")]
        public async Task<IActionResult> PromoteUserToHost(string uid)
        {
            _logger.LogDebugWithClientData(Request, "Adding Role Claim", new Dictionary<string, string>()
            {
                { "Role value", "Host" },
                { "UID", uid },
            });

            var userRole = await _userRoleService.PromoteUserToHost(uid);
            return Ok(userRole.Adapt<UserRole>());
        }

        [HttpPost("{uid}/prmoteUserToAdmin")]
        public async Task<IActionResult> PromoteUserToAdmin(string uid)
        {
            _logger.LogDebugWithClientData(Request, "Adding Role Claim", new Dictionary<string, string>()
            {
                { "Role value", "Admin" },
                { "UID", uid },
            });

            var userRole = await _userRoleService.PromoteUserToAdmin(uid);
            return Ok(userRole.Adapt<UserRole>());
        }

        [HttpPost("{uid}/clearUserRole")]
        public async Task<IActionResult> ClearUserRole(string uid)
        {
            await _userRoleService.ClearUserRole(uid);
            return NoContent();
        }

    }
}
