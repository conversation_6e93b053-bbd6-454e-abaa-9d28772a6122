using System.Net;

namespace Savvy.AppBackend.Server.Extensions
{
    public static class HttpRequestExtensions
    {
        public static IPAddress? GetRemoteIpAddress(this HttpRequest httpRequest)
        {
            return httpRequest.HttpContext?.Connection?.RemoteIpAddress.MapToIPv4();
        }

        public static string GetRequestedMethod(this HttpRequest httpRequest)
        {
            return httpRequest.Method;
        }

        public static string GetRequestPath(this HttpRequest httpRequest)
        {
            return httpRequest.Path;
        }
    }
}
