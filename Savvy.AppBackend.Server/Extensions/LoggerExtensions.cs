using System.Text;

namespace Savvy.AppBackend.Server.Extensions
{
    public static class LoggerExtensions
    {
        public static void LogInformationWithClientData(this ILogger logger, HttpRequest httpRequest, string eventName, IDictionary <string, string>? additionalParams = null)
        {
            logger.LogInformation(BuildLogMessage(httpRequest, eventName, additionalParams));
        }

        public static void LogWarningWithClientData(this ILogger logger, HttpRequest httpRequest, string eventName, IDictionary<string, string>? additionalParams = null)
        {
            logger.LogWarning(BuildLogMessage(httpRequest, eventName, additionalParams));
        }

        public static void LogErrorWithClientData(this ILogger logger, HttpRequest httpRequest, string eventName, IDictionary<string, string>? additionalParams = null)
        {
            logger.LogError(BuildLogMessage(httpRequest, eventName, additionalParams));
        }

        public static void LogDebugWithClientData<T>(this ILogger<T> logger, HttpRequest httpRequest, string eventName, IDictionary<string, string>? additionalParams = null)
            where T : class
        {
            logger.LogDebug(BuildLogMessage(httpRequest, eventName, additionalParams));
        }

        private static string BuildLogMessage(HttpRequest httpRequest, string eventName, IDictionary<string, string>? additionalParams = null)
        {
            var stringBuilder = new StringBuilder();
            stringBuilder.Append($"{eventName}. Remote IP: {httpRequest.GetRemoteIpAddress()}, Method: {httpRequest.GetRequestedMethod()}, Path: {httpRequest.GetRequestPath()}");

            if (additionalParams?.Count > 0)
            {
                foreach (var item in additionalParams)
                {
                    stringBuilder.Append($", {item.Key}: {item.Value}");
                }
            }

            return stringBuilder.ToString();
        }
    }
}
