using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;
using Savvy.AppBackend.Server.Middleware;

public class IgnoreMethodParameterTransformer : IOpenApiOperationTransformer
{
    public Task TransformAsync(OpenApiOperation operation, OpenApiOperationTransformerContext context, CancellationToken cancellationToken)
    {
        if (operation.Parameters?.Count() > 0)
        {
            var userIdMethodParameter = operation.Parameters?.FirstOrDefault(e => e.Name == FromJwtAttribute.USER_ID_METHOD_PARAMETER_NAME);
            if (userIdMethodParameter is not null)
            {
                operation.Parameters?.Remove(userIdMethodParameter);
            }
        }

        return Task.CompletedTask;
    }
}