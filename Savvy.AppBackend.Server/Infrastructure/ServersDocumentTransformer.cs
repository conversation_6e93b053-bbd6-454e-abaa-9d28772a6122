using Microsoft.AspNetCore.OpenApi;
using Microsoft.OpenApi.Models;

namespace Savvy.AppBackend.Server.Infrastructure
{
    public class ServersDocumentTransformer : IOpenApiDocumentTransformer
    {
        public Task TransformAsync(OpenApiDocument document, OpenApiDocumentTransformerContext context, CancellationToken cancellationToken)
        {
            var https = "https";
            var apiAddress = document.Servers.First().Url;
            if (!apiAddress.StartsWith(https))
            {
                document.Servers.Clear();
                document.Servers.Add(new OpenApiServer { Url = apiAddress.Replace("http", https) });
            }

            return Task.CompletedTask;
        }
    }
}
