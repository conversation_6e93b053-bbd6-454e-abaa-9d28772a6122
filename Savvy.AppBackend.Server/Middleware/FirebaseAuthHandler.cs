using FirebaseAdmin.Auth;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Options;
using Savvy.AppBackend.Server.Extensions;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Savvy.AppBackend.Server.Middleware
{
    public class FirebaseAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        public FirebaseAuthenticationHandler(IOptionsMonitor<AuthenticationSchemeOptions> options, ILoggerFactory logger, UrlEncoder encoder, ISystemClock clock)
            : base(options, logger, encoder, clock)
        {
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            Logger.LogInformationWithClientData(Request, "Authentication start");

            var authorization = Request.Headers["Authorization"];
            if (string.IsNullOrEmpty(authorization))
            {
                Logger.LogWarningWithClientData(Request, "Missing Authorization");
                
                return AuthenticateResult.Fail("No authorization header");
            }

            string token = authorization.FirstOrDefault()!.Split(" ").Last();

            if (string.IsNullOrEmpty(token))
            {
                Logger.LogWarningWithClientData(Request, "Missing Token");
                return AuthenticateResult.Fail("No token");
            }

            try
            {
                var decoded = await FirebaseAuth.DefaultInstance.VerifyIdTokenAsync(token);

                var uid = decoded.Uid;

                var claims = new List<Claim>();

                foreach (var kvp in decoded.Claims)
                {
                    if (kvp.Key == "role")
                        claims.Add(new Claim(kvp.Key, kvp.Value.ToString() ?? "user"));
                    if (kvp.Key == FromJwtAttribute.JWT_TOKEN_CLAIM_NAME)
                        claims.Add(new Claim(kvp.Key, kvp.Value.ToString() ?? string.Empty));
                }

                var identity = new ClaimsIdentity(claims, nameof(FirebaseAuthenticationHandler));
                var principal = new ClaimsPrincipal(identity);
                var ticket = new AuthenticationTicket(principal, Scheme.Name);

                Logger.LogInformationWithClientData(Request, "Successful Authentication", new Dictionary<string, string>() { { "UID", uid.ToString() } });
                return AuthenticateResult.Success(ticket);
            }
            catch (FirebaseAuthException authException)
            {
                Logger.LogWarningWithClientData(Request, "Firebase Auth Failure", new Dictionary<string, string>() { {"Exception message", authException.Message} });
                return AuthenticateResult.Fail($"Firebase auth failed: {authException.Message}");
            }
            catch (Exception ex)
            {
                Logger.LogErrorWithClientData(Request, "Unauthorized", new Dictionary<string, string>() { { "Exception message", ex.Message } });
                return AuthenticateResult.Fail($"Firebase auth failed: {ex.Message}");
            }
        }
    }

}
