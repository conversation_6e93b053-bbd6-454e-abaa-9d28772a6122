using Microsoft.AspNetCore.Mvc.ModelBinding;
using System.Security.Claims;

namespace Savvy.AppBackend.Server.Middleware
{
    [AttributeUsage(AttributeTargets.Parameter)]
    public class FromJwtAttribute : Attribute, IModelBinder
    {
        public static string JWT_TOKEN_CLAIM_NAME = "user_id";
        public static string USER_ID_METHOD_PARAMETER_NAME = "userId";

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            var httpContext = bindingContext.HttpContext;

            var userId = httpContext.User.FindFirstValue(JWT_TOKEN_CLAIM_NAME);

            if (userId != null && !string.IsNullOrWhiteSpace(userId))
            {
                bindingContext.Result = ModelBindingResult.Success(userId);
            }
            else
            {
                bindingContext.ModelState.TryAddModelError(
                    bindingContext.FieldName,
                    "User ID not found in JWT token."
                );
            }

            return Task.CompletedTask;
        }
    }

    public class FromJwtBinderProvider : IModelBinderProvider
    {
        public IModelBinder GetBinder(ModelBinderProviderContext context)
        {
            if (context.Metadata.ParameterName == FromJwtAttribute.USER_ID_METHOD_PARAMETER_NAME &&
                context.Metadata.ModelType == typeof(string))
            {
                return new FromJwtAttribute();
            }

            return null;
        }
    }
}
