using Savvy.AppBackend.Domain.Models;
using System.ComponentModel.DataAnnotations;

namespace Savvy.AppBackend.Server.Models;

public class CreateStreamDetailRequest
{
    [Required]
    public required string HostId { get; set; }
    [Required, Length(3, 100)]
    public required string Title { get; set; }
    public DateTimeOffset StartDate { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public string HostName { get; set; } = string.Empty;
    public StreamType Type { get; set; }
    public int Prize { get; set; } = 0;
}
