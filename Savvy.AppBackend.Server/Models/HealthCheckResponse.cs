using Savvy.AppBackend.Domain.Models;

namespace Savvy.AppBackend.Server.Models;

public class HealthCheckResponse
{
    public string ServiceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public double ResponseTimeMs { get; set; }
    public DateTime CheckedAt { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();

    public static HealthCheckResponse FromServiceHealthCheck(ServiceHealthCheck serviceHealth)
    {
        return new HealthCheckResponse
        {
            ServiceName = serviceHealth.ServiceName,
            Status = serviceHealth.Status.ToString(),
            Description = serviceHealth.Description,
            ResponseTimeMs = serviceHealth.ResponseTime.TotalMilliseconds,
            CheckedAt = serviceHealth.CheckedAt,
            Data = serviceHealth.Data
        };
    }
}

public class OverallHealthCheckResponse
{
    public string appVersion { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime CheckedAt { get; set; }
    public double TotalResponseTimeMs { get; set; }
    public List<HealthCheckResponse> Services { get; set; } = new();

    public static OverallHealthCheckResponse FromOverallHealthCheck(OverallHealthCheck overallHealth)
    {
        return new OverallHealthCheckResponse
        {
            appVersion = Environment.GetEnvironmentVariable("APP_VERSION"),
            Status = overallHealth.Status.ToString(),
            Description = overallHealth.Description,
            CheckedAt = overallHealth.CheckedAt,
            TotalResponseTimeMs = overallHealth.TotalResponseTime.TotalMilliseconds,
            Services = overallHealth.Services.Select(HealthCheckResponse.FromServiceHealthCheck).ToList()
        };
    }
}
