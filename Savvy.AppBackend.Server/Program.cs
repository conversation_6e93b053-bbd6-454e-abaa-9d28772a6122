using Savvy.AppBackend.Server;
using Savvy.AppBackend.Infrastructure;
using Savvy.AppBackend.Application;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Server.Infrastructure.Exceptions;
using Savvy.AppBackend.Server.Infrastructure;
using Savvy.AppBackend.Server.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddApplicationInsightsTelemetry();

builder.Services.AddControllers(options =>
{
    options.ModelBinderProviders.Insert(0, new FromJwtBinderProvider());
});

builder.Services.AddOpenApi(options =>
{
    options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
    options.AddOperationTransformer<IgnoreMethodParameterTransformer>();
});

builder.Services.AddCosmosDb(builder.Configuration);

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

builder.Services.AddFirebaseAuthenticationAndAuthorization(builder.Configuration);

builder.Services.AddInfrastructure();

builder.Services.AddApplication();

builder.Services.AddOpenApi();

var app = builder.Build();

app.MapOpenApi();
app.UseSwaggerUI(options =>
{
    options.SwaggerEndpoint("/openapi/v1.json", "v1");
});

app.UseExceptionHandler(options => { });

app.UseHttpsRedirection();

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();