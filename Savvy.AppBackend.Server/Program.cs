using Savvy.AppBackend.Server;
using Savvy.AppBackend.Infrastructure;
using Savvy.AppBackend.Application;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Server.Infrastructure.Exceptions;
using Savvy.AppBackend.Server.Infrastructure;
using Savvy.AppBackend.Server.Middleware;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

var keyVaultUri = Environment.GetEnvironmentVariable("KEY_VAULT_URI");
var kvClient = new SecretClient(new Uri(keyVaultUri), new DefaultAzureCredential());
var builder = WebApplication.CreateBuilder(args);

var port = Environment.GetEnvironmentVariable("PORT") ?? "80";
builder.WebHost.UseUrls($"http://*:{port}");

builder.Services.AddApplicationInsightsTelemetry();

builder.Services.AddControllers(options =>
{
    options.ModelBinderProviders.Insert(0, new FromJwtBinderProvider());
});

builder.Services.AddOpenApi(options =>
{
    options.AddDocumentTransformer<ServersDocumentTransformer>();
    options.AddDocumentTransformer<BearerSecuritySchemeTransformer>();
    options.AddOperationTransformer<IgnoreMethodParameterTransformer>();
});

builder.Services.AddCosmosDb(kvClient);

builder.Services.AddExceptionHandler<GlobalExceptionHandler>();

builder.Services.AddFirebaseAuthenticationAndAuthorization(kvClient);

builder.Services.AddInfrastructure();

builder.Services.AddApplication();

builder.Services.AddOpenApi();

var app = builder.Build();

app.UseHttpsRedirection();

app.MapOpenApi();
app.UseSwaggerUI(options =>
{
    options.SwaggerEndpoint("/openapi/v1.json", "v1");
});

app.UseExceptionHandler(options => { });

app.UseAuthentication();

app.UseAuthorization();

app.MapControllers();

app.Run();