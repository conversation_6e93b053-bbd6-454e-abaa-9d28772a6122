<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.14.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.7.0" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Savvy.AppBackend.Application\Savvy.AppBackend.Application.csproj" />
    <ProjectReference Include="..\Savvy.AppBackend.Domain\Savvy.AppBackend.Domain.csproj" />
    <ProjectReference Include="..\Savvy.AppBackend.Infrastructure\Savvy.AppBackend.Infrastructure.csproj" />
  </ItemGroup>

</Project>
