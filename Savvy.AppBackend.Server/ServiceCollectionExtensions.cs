using FirebaseAdmin.Auth;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.AspNetCore.Authentication;
using Savvy.AppBackend.Server.Middleware;
using FirebaseAdmin.Messaging;
using Azure.Security.KeyVault.Secrets;


namespace Savvy.AppBackend.Server
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddFirebaseAuthenticationAndAuthorization(this IServiceCollection services, SecretClient kvClient)
        {
            KeyVaultSecret firebaseCredentialsSecret = kvClient.GetSecret("firebase-credentials-json");
            FirebaseApp.Create(new AppOptions()
            {
                Credential = GoogleCredential.FromJson(firebaseCredentialsSecret.Value)
            });

            services.AddSingleton(FirebaseAuth.DefaultInstance);

            services.AddSingleton(FirebaseMessaging.DefaultInstance);

            var authenticationScheme = "Firebase";
            services.AddAuthentication(authenticationScheme)
                .AddScheme<AuthenticationSchemeOptions, FirebaseAuthenticationHandler>(authenticationScheme, null)
                .AddJwtBearer();

            services.AddAuthorization(options =>
            {
                options.AddPolicy("HostOnly", policy => policy.RequireClaim("role", "host"));
                options.AddPolicy("AdminOnly", policy => policy.RequireClaim("role", "admin"));
            });

            return services;
        }


    }
}
