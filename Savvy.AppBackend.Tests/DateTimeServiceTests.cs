using Savvy.AppBackend.Application.Services;

namespace Savvy.AppBackend.Tests
{
    public class DateTimeServiceTests
    {
        [Fact]
        public void GetStartAndEndOfTheWeek_ShouldReturnStartAndEndOfTheWeek()
        {
            // Arrange

            var dateTimeService = new DateTimeService();

            // Act
            var result = dateTimeService.GetStartAndEndOfTheGameWeek();

            // Assert
            Assert.Equal(DayOfWeek.Sunday, result.StartOfRange.DayOfWeek);
            Assert.Equal(DayOfWeek.Thursday, result.EndOfRange.DayOfWeek);
        }

        [Fact]
        public void GetStartAndEndOfTheFullWeek_ShouldReturnStartAndEndOfTheWeek()
        {
            // Arrange

            var dateTimeService = new DateTimeService();

            // Act
            var result = dateTimeService.GetStartAndEndOfTheFullWeek();

            // Assert
            Assert.Equal(DayOfWeek.Sunday, result.StartOfRange.DayOfWeek);
            Assert.Equal(DayOfWeek.Saturday, result.EndOfRange.DayOfWeek);
        }

        [Fact]
        public void GetStartAndEndOfCurrentMonth_ShouldReturnStartAndEndOfTheWeek()
        {
            // Arrange
            DateTime today = DateTime.Today;
            int daysInMonth = DateTime.DaysInMonth(today.Year, today.Month);
            DateTime startDate = new DateTime(today.Year, today.Month, 1);
            DateTime endDate = new DateTime(today.Year, today.Month, daysInMonth);

            var dateTimeService = new DateTimeService();

            // Act
            var result = dateTimeService.GetStartAndEndOfCurrentMonth();

            // Assert
            Assert.Equal(startDate.Date, result.StartOfRange.Date);
            Assert.Equal(endDate.Date, result.EndOfRange.Date);
        }
    }
}
