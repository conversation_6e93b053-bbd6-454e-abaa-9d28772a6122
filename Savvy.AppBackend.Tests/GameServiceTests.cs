using Moq;
using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Tests
{
    public class GameServiceTests
    {
        [Fact]
        public async Task GetWeeklyGamesAndDetailsAsync_WhenStreamsExistsAndOneMoneyGame_ShouldReturnDetails()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var streamId = Guid.NewGuid().ToString();
            var userGameService = new Mock<IUserGameService>();
            var userGameDetails = new[]
            {
                new UserGameDetails
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    StreamId = streamId,
                    IsUserQualifiedToMoneyGame = true,
                    GameScore = 3214,
                    StreamStartDate = DateTime.UtcNow,
                }
            };

            userGameService.Setup(e => e.GetUserGameDetailsByStreamsAndUser(It.IsAny<string[]>(), It.IsAny<string>()))
                .ReturnsAsync(userGameDetails);

            var streamService = new Mock<IStreamService>();
            var streams = new[]
            {
                new StreamDetail
                {
                    Id = streamId,
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now.AddDays(-2),
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now.AddDays(-1),
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.PrizeGame,
                    StartDate = DateTime.Now,
                    Prize = 1234
                }
            };

            streamService.Setup(e => e.GetWeeklyStreams()).ReturnsAsync(streams);

            var homeService = new GameService(userGameService.Object, streamService.Object);

            // Act
            var result = await homeService.GetWeeklyGamesAndDetailsAsync(userId);

            // Assert
            var expectedNumberOfGames = 3;
            Assert.NotNull(result);
            Assert.Equal(expectedNumberOfGames, result.WeeklyGames.Count());
            Assert.NotNull(result.WeeklyGames.FirstOrDefault());
            Assert.Equal(streams.First().Id, result.WeeklyGames.First().Id);
            Assert.NotNull(result.WeeklyGames.LastOrDefault());
            Assert.Equal(streams.Last().Id, result.WeeklyGames.Last().Id);
        }

        [Fact]
        public async Task GetWeeklyGamesAndDetailsAsync_WhenStreamsExistsAndTwoMoneyGame_ShouldReturnDetails()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var streamId = Guid.NewGuid().ToString();
            var userGameService = new Mock<IUserGameService>();
            var userGameDetails = new[]
            {
                new UserGameDetails
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    StreamId = streamId,
                    IsUserQualifiedToMoneyGame = true,
                    GameScore = 3214,
                    StreamStartDate = DateTime.UtcNow,
                }
            };

            userGameService.Setup(e => e.GetUserGameDetailsByStreamsAndUser(It.IsAny<string[]>(), It.IsAny<string>()))
                .ReturnsAsync(userGameDetails);

            var streamService = new Mock<IStreamService>();
            var streams = new[]
            {
                new StreamDetail
                {
                    Id = streamId,
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now.AddDays(-2),
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.PrizeGame,
                    StartDate = DateTime.Now.AddDays(-1),
                    Prize = 1234,
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.PrizeGame,
                    StartDate = DateTime.Now,
                    Prize = 4321
                }
            };

            streamService.Setup(e => e.GetWeeklyStreams()).ReturnsAsync(streams);

            var homeService = new GameService(userGameService.Object, streamService.Object);

            // Act
            var result = await homeService.GetWeeklyGamesAndDetailsAsync(userId);

            // Assert
            var expectedNumberOfGames = 4;
            Assert.NotNull(result);
            Assert.Equal(expectedNumberOfGames, result.WeeklyGames.Count());
            Assert.NotNull(result.WeeklyGames.FirstOrDefault());
            Assert.Equal(streams.First().Id, result.WeeklyGames.First().Id);
            Assert.NotNull(result.WeeklyGames.LastOrDefault());
            Assert.Equal(streams.Last().Id, result.WeeklyGames.Last().Id);
        }

        [Fact]
        public async Task GetWeeklyGamesAndDetailsAsync_WhenStreamsExistsWithoutMoneyGame_ShouldReturnDetails()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var streamId = Guid.NewGuid().ToString();
            var userGameService = new Mock<IUserGameService>();
            var userGameDetails = new[]
            {
                new UserGameDetails
                {
                    Id = Guid.NewGuid().ToString(),
                    UserId = userId,
                    StreamId = streamId,
                    IsUserQualifiedToMoneyGame = true,
                    GameScore = 3214,
                    StreamStartDate = DateTime.UtcNow,
                }
            };

            userGameService.Setup(e => e.GetUserGameDetailsByStreamsAndUser(It.IsAny<string[]>(), It.IsAny<string>()))
                .ReturnsAsync(userGameDetails);

            var streamService = new Mock<IStreamService>();
            var streams = new[]
            {
                new StreamDetail
                {
                    Id = streamId,
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now.AddDays(-2),
                },
                new StreamDetail
                {
                    Id = Guid.NewGuid().ToString(),
                    HostId = Guid.NewGuid().ToString(),
                    Title = "Test",
                    ImageUrl = "Image",
                    HostName = "TestHOst",
                    Type = StreamType.QualifierGame,
                    StartDate = DateTime.Now
                }
            };

            streamService.Setup(e => e.GetWeeklyStreams()).ReturnsAsync(streams);

            var homeService = new GameService(userGameService.Object, streamService.Object);

            // Act
            var result = await homeService.GetWeeklyGamesAndDetailsAsync(userId);

            // Assert
            var expectedNumberOfGames = 2;
            Assert.NotNull(result);
            Assert.Equal(expectedNumberOfGames, result.WeeklyGames.Count());
        }
    }
}
