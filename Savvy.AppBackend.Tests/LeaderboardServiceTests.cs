using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Domain.Exceptions;
using Moq;
using Savvy.AppBackend.Domain.Services;
using Savvy.AppBackend.Domain.Models.Leaderboard;
using Savvy.AppBackend.Domain.Models.Enums;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.UserProfile;

namespace Savvy.AppBackend.Tests;

public class LeaderboardServiceTests
{
    [Fact]
    public async Task GetLeaderboardAsync_WhenTakePaginationParamsIsNotValid_ShouldThrowError()
    {
        // Arrange
        var userProfileService = new Mock<IUserProfileService>();
        var userGameService = new Mock<IUserGameService>();
        var leaderboardService = new LeaderboardService(userGameService.Object, userProfileService.Object);
        var notValidLimitParam = -10;
        var skip = 1;

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(async () => await leaderboardService.GetLeaderboardAsync(TimeFilter.Weekly, notValidLimitParam, skip));
    }

    [Fact]
    public async Task GetLeaderboardAsync_WhenPagePaginationParamsIsNotValid_ShouldThrowError()
    {
        // Arrange
        var userProfileService = new Mock<IUserProfileService>();
        var userGameService = new Mock<IUserGameService>();
        var leaderboardService = new LeaderboardService(userGameService.Object, userProfileService.Object);
        var limit = 10;
        var notValidSkipParam = -5;

        // Act & Assert
        await Assert.ThrowsAsync<ValidationException>(async () => await leaderboardService.GetLeaderboardAsync(TimeFilter.Weekly, limit, notValidSkipParam));
    }

    [Fact]
    public async Task GetWeeklyLeaderboardAsync_WhenLeaderboardRequested_ReturnsSortedCollection()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var secondUserId = Guid.NewGuid().ToString();
        var thirdUserId = Guid.NewGuid().ToString();
        var userProfileDocument = new List<UserProfileShortInfo> {
            new() {
                Id = firstUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            }
        }.ToList();

        var userGameService = new Mock<IUserGameService>();
        userGameService.Setup(e => e.GetWeeklyUserGameResults()).ReturnsAsync(new List<UserGameDetails>
        {
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = thirdUserId,
                GameScore = 5100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = true,
                StreamStartDate = DateTime.Now,
            },
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = secondUserId,
                GameScore = 2100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = false,
                StreamStartDate = DateTime.Now,
            },
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = firstUserId,
                GameScore = 100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = false,
                StreamStartDate = DateTime.Now,
            }
        });

        var userProfileService = new Mock<IUserProfileService>();
        userProfileService.Setup(e => e.GetListOfUsersByIdAsync(It.IsAny<string[]>())).ReturnsAsync(userProfileDocument);

        var take = 10;
        var skip = 1;
        var leaderboardService = new LeaderboardService(userGameService.Object, userProfileService.Object);

        // Act
        var leaderboard = await leaderboardService.GetLeaderboardAsync(TimeFilter.Weekly, take, skip);

        // Assert
        Assert.True(leaderboard.Count() == userProfileDocument.Count());
        Assert.True(leaderboard.First().TotalScore == 100);
    }

    [Fact]
    public async Task GetMonthlyLeaderboardAsync_WhenLeaderboardRequested_ReturnsSortedCollection()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var secondUserId = Guid.NewGuid().ToString();
        var thirdUserId = Guid.NewGuid().ToString();
        var userProfileDocument = new List<UserProfileShortInfo> {
            new() {
                Id = firstUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            },
            new() {
                Id = secondUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            }
        }.ToList();

        var userGameService = new Mock<IUserGameService>();
        userGameService.Setup(e => e.GetMonthlyUserGameResults()).ReturnsAsync(new List<UserGameDetails>
        {
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = thirdUserId,
                GameScore = 5100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = true,
                StreamStartDate = DateTime.Now,
            },
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = secondUserId,
                GameScore = 2100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = false,
                StreamStartDate = DateTime.Now,
            },
            new UserGameDetails
            {
                Id = Guid.NewGuid().ToString(),
                UserId = firstUserId,
                GameScore = 100,
                StreamId = Guid.NewGuid().ToString(),
                IsUserQualifiedToMoneyGame = false,
                StreamStartDate = DateTime.Now,
            }
        });

        var userProfileService = new Mock<IUserProfileService>();
        userProfileService.Setup(e => e.GetListOfUsersByIdAsync(It.IsAny<string[]>())).ReturnsAsync(userProfileDocument);

        var take = 10;
        var skip = 1;
        var leaderboardService = new LeaderboardService(userGameService.Object, userProfileService.Object);

        // Act
        var leaderboard = await leaderboardService.GetLeaderboardAsync(TimeFilter.Monthly, take, skip);

        // Assert
        Assert.True(leaderboard.Count() == userProfileDocument.Count());
        Assert.True(leaderboard.First().TotalScore == 2100);
        Assert.True(leaderboard.Last().TotalScore == 100);
    }

    [Fact]
    public async Task GetAllTimeLeaderboardAsync_WhenLeaderboardRequested_ReturnsSortedCollection()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var secondUserId = Guid.NewGuid().ToString();
        var thirdUserId = Guid.NewGuid().ToString();
        var userProfileDocument = new List<UserProfileShortInfo> {
            new() {
                Id = firstUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            },
            new() {
                Id = secondUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            },
            new() {
                Id = thirdUserId,
                Username = "TestUserName",
                ImageUrl = "image"
            },
        }.ToList();

        var userGameService = new Mock<IUserGameService>();
        userGameService.Setup(e => e.GetAllTimeUserGameDetails(It.IsAny<int>(), It.IsAny<int>())).ReturnsAsync(new List<UserScoreDetails>
        {
            new UserScoreDetails
            {
                UserId = thirdUserId,
                TotalScore = 5100,
            },
            new UserScoreDetails
            {
                UserId = secondUserId,
                TotalScore = 2100,
            },
            new UserScoreDetails
            {
                UserId = firstUserId,
                TotalScore = 100,
            }
        });

        var userProfileService = new Mock<IUserProfileService>();
        userProfileService.Setup(e => e.GetListOfUsersByIdAsync(It.IsAny<string[]>())).ReturnsAsync(userProfileDocument);

        var take = 10;
        var skip = 0;
        var leaderboardService = new LeaderboardService(userGameService.Object, userProfileService.Object);

        // Act
        var leaderboard = await leaderboardService.GetLeaderboardAsync(TimeFilter.All, take, skip);

        // Assert
        Assert.True(leaderboard.Count() == userProfileDocument.Count());
        Assert.True(leaderboard.Last().TotalScore == 5100);
        Assert.True(leaderboard.First().TotalScore == 100);
    }
}
