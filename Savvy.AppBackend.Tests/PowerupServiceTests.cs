using Moq;
using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Models.PowerupModels;

namespace Savvy.AppBackend.Tests
{
    public class PowerupServiceTests
    {
        private const string ContainerId = CosmosDbContainers.PowerupsContainerId;

        [Fact]
        public async Task GetUsersPowerups_WhenRecordsExists_ShouldReturnListOfPowerups()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var powerupsDocuments = new List<PowerupDocument>()
            {
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 1,
                    Count = 10,
                    UserId = userId
                },
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 2,
                    Count = 3,
                    UserId = userId
                }
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<PowerupDocument>, IQueryable<PowerupDocument>>>()))
                .ReturnsAsync(powerupsDocuments);

            var service = new PowerupService(cosmosDbRepository.Object);

            // Act
            var result = await service.GetUsersPowerups(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(result.First().PowerupId, powerupsDocuments.First().PowerupId);
            Assert.Equal(result.First().Count, powerupsDocuments.First().Count);
            Assert.Equal(result.Last().PowerupId, powerupsDocuments.Last().PowerupId);
            Assert.Equal(result.Last().Count, powerupsDocuments.Last().Count);
        }

        [Fact]
        public async Task UpdatePowerupsNumber_WhenRecordsNotExists_ShouldAddPassedObjects()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var powerups = new List<PowerupShortInfo>()
            {
                new PowerupShortInfo
                {
                    PowerupId = 1,
                    Count = 10,
                },
                new PowerupShortInfo
                {
                    PowerupId = 2,
                    Count = 3
                }
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<PowerupDocument>, IQueryable<PowerupDocument>>>()))
                .ReturnsAsync([]);

            var service = new PowerupService(cosmosDbRepository.Object);

            // Act
            var result = await service.UpdatePowerupsNumber(powerups, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(result.First().PowerupId, powerups.First().PowerupId);
            Assert.Equal(result.First().Count, powerups.First().Count);
            Assert.Equal(result.Last().PowerupId, powerups.Last().PowerupId);
            Assert.Equal(result.Last().Count, powerups.Last().Count);
            
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == powerups.First().Count)), Times.Once());
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == powerups.Last().Count)), Times.Once());
        }

        [Fact]
        public async Task UpdatePowerupsNumber_WhenRecordsExists_ShouldIncreaseNumber()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var powerupsDocuments = new List<PowerupDocument>()
            {
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 1,
                    Count = 10,
                    UserId = userId
                },
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 2,
                    Count = 3,
                    UserId = userId
                }
            };

            var powerups = new List<PowerupShortInfo>()
            {
                new PowerupShortInfo
                {
                    PowerupId = 1,
                    Count = 5,
                },
                new PowerupShortInfo
                {
                    PowerupId = 2,
                    Count = 7
                }
            };

            var firstExpectedPowerupCount = powerupsDocuments.First().Count + powerups.First().Count;
            var secondExpectedPowerupCount = powerupsDocuments.Last().Count + powerups.Last().Count;

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<PowerupDocument>, IQueryable<PowerupDocument>>>()))
                .ReturnsAsync(powerupsDocuments);

            var service = new PowerupService(cosmosDbRepository.Object);

            // Act
            var result = await service.UpdatePowerupsNumber(powerups, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(result.First().PowerupId, powerups.First().PowerupId);
            Assert.Equal(result.First().Count, firstExpectedPowerupCount);
            Assert.Equal(result.Last().PowerupId, powerups.Last().PowerupId);
            Assert.Equal(result.Last().Count, secondExpectedPowerupCount);

            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == firstExpectedPowerupCount)), Times.Once());
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == secondExpectedPowerupCount)), Times.Once());
        }

        [Fact]
        public async Task UpdatePowerupsNumber_WhenRecordsExists_ShouldDecreaseNumber()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var powerupsDocuments = new List<PowerupDocument>()
            {
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 1,
                    Count = 10,
                    UserId = userId
                },
                new PowerupDocument
                {
                    Id = Guid.NewGuid().ToString(),
                    PowerupId = 2,
                    Count = 3,
                    UserId = userId
                }
            };

            var powerups = new List<PowerupShortInfo>()
            {
                new PowerupShortInfo
                {
                    PowerupId = 1,
                    Count = -5,
                },
                new PowerupShortInfo
                {
                    PowerupId = 2,
                    Count = -7
                }
            };

            var firstExpectedPowerupCount = powerupsDocuments.First().Count + powerups.First().Count;
            var secondExpectedPowerupCount = 0;

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<PowerupDocument>, IQueryable<PowerupDocument>>>()))
                .ReturnsAsync(powerupsDocuments);

            var service = new PowerupService(cosmosDbRepository.Object);

            // Act
            var result = await service.UpdatePowerupsNumber(powerups, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(result.First().PowerupId, powerups.First().PowerupId);
            Assert.Equal(result.First().Count, firstExpectedPowerupCount);
            Assert.Equal(result.Last().PowerupId, powerups.Last().PowerupId);
            Assert.Equal(result.Last().Count, secondExpectedPowerupCount);

            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == firstExpectedPowerupCount)), Times.Once());
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<PowerupDocument>(x => x.Count == secondExpectedPowerupCount)), Times.Once());
        }
    }
}
