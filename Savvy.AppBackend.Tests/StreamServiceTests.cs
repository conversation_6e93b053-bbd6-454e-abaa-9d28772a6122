using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Domain.Models;
using StreamStatus = Savvy.AppBackend.Domain.Models.StreamStatus;
using Moq;
using Savvy.AppBackend.Domain.Services;
using Microsoft.Azure.Cosmos.Linq;

namespace Savvy.AppBackend.Tests;

public class StreamServiceTests
{
    private const string ContainerId = "streams";

    [Fact]
    public async Task GetScheduledStreams_Can_RetrieveStreams()
    {
        // Arrange
        var dateTimeService = new Mock<IDateTimeService>();
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync<StreamDocument>(ContainerId, It.IsAny<Func<IOrderedQueryable<StreamDocument>, IQueryable<StreamDocument>>>()))
            .ReturnsAsync(
            [
                new StreamDocument
                {
                    HostId = "1",
                    Title = "Test",
                    HostName = "Test Host",
                    ImageUrl = "https://example.com/image.jpg",
                }
            ]);
        var streamService = new StreamService(cosmosDbRepository.Object, dateTimeService.Object);

        // Act
        var streams = (await streamService.GetScheduledStreamsAsync()).ToList();

        // Assert
        Assert.Single(streams);
        Assert.Equal("1", streams[0].HostId);
        Assert.Equal("Test", streams[0].Title);
    }

    [Fact]
    public async Task Can_Create_Stream()
    {
        // Arrange
        var streamDetail = new StreamDetail
        {
            HostId = "1",
            Id = "1",
            Title = "Test",
            HostName = "Test Host",
            ImageUrl = "https://example.com/image.jpg",
        };
        var dateTimeService = new Mock<IDateTimeService>();
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.UpsertItemAsync(ContainerId, It.IsAny<StreamDocument>()))
            .ReturnsAsync(new StreamDocument
            {
                HostId = "1",
                Title = "Test",
                HostName = "Test Host",
                ImageUrl = "https://example.com/image.jpg",
            });
        var streamService = new StreamService(cosmosDbRepository.Object, dateTimeService.Object);

        // Act
        var newStreamDetail = await streamService.CreateStreamAsync(streamDetail);

        // Assert
        Assert.Equal("1", newStreamDetail.HostId);
        Assert.Equal("Test", newStreamDetail.Title);
        Assert.Equal(StreamStatus.Scheduled, newStreamDetail.Status);
    }

    [Fact]
    public async Task Can_Update_Stream()
    {
        // Arrange
        var id = "1";
        var hostId = "2";
        var streamDetail = new StreamDetail
        {
            HostId = hostId,
            Id = id,
            Title = "Test",
            HostName = "Test Host",
            ImageUrl = "https://example.com/image.jpg",
        };
        var streamDocument = new StreamDocument
        {
            Id = id,
            HostId = hostId,
            Title = "Test",
            HostName = "Test Host",
            ImageUrl = "https://example.com/image.jpg",
        };
        var dateTimeService = new Mock<IDateTimeService>();
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<StreamDocument>(ContainerId, id, hostId))
            .ReturnsAsync(streamDocument);

        cosmosDbRepository.Setup(e => e.UpsertItemAsync(ContainerId, streamDocument)).ReturnsAsync(streamDocument);
        var streamService = new StreamService(cosmosDbRepository.Object, dateTimeService.Object);

        // Act
        await streamService.UpdateStreamAsync(id, hostId, streamDetail);
    }

    [Fact]
    public async Task Can_Update_Status()
    {
        // Arrange
        var id = "1";
        var hostId = "2";
        var streamDetail = new StreamDetail
        {
            HostId = hostId,
            Id = id,
            Title = "Test",
            Status = StreamStatus.Completed,
            HostName = "Test Host",
            ImageUrl = "https://example.com/image.jpg",
        };
        var streamDocument = new StreamDocument
        {
            Id = id,
            HostId = hostId,
            Title = "Test",
            HostName = "Test Host",
            ImageUrl = "https://example.com/image.jpg",
        };
        var dateTimeService = new Mock<IDateTimeService>();
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<StreamDocument>(ContainerId, id, hostId))
            .ReturnsAsync(streamDocument);

        cosmosDbRepository.Setup(e => e.UpsertItemAsync(ContainerId, streamDocument)).ReturnsAsync(streamDocument);
        var streamService = new StreamService(cosmosDbRepository.Object, dateTimeService.Object);

        // Act
        await streamService.UpdateStreamAsync(id, hostId, streamDetail);

        // Assert
        Assert.Equal(StreamStatus.Completed, (StreamStatus)streamDocument.Status);
    }

    [Fact]
    public async Task GetWeeklyStreams_WhenDataExists_ShouldReturnWeeklyStreams()
    {
        // Arrange
        var documents = new List<StreamDocument>
        {
            new StreamDocument
            {
                Id = Guid.NewGuid().ToString(),
                HostId = Guid.NewGuid().ToString(),
                Title = "Test",
                HostName = "Test",
                ImageUrl = "imageUrl"
            },
            new StreamDocument
            {
                Id = Guid.NewGuid().ToString(),
                HostId = Guid.NewGuid().ToString(),
                Title = "Test",
                HostName = "Test",
                ImageUrl = "imageUrl"
            }
        };
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<StreamDocument>, IQueryable<StreamDocument>>>()))
            .ReturnsAsync(documents);

        var streamService = new StreamService(cosmosDbRepository.Object, new DateTimeService());

        // Act
        var result = await streamService.GetWeeklyStreams();

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.FirstOrDefault());
        Assert.Equal(documents.First().Id, result.First().Id);
        Assert.Equal(documents.Last().Id, result.Last().Id);
    }
}
