using Mapster;
using Microsoft.Azure.Cosmos;
using Moq;
using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.Leaderboard;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Tests
{
    public class UserGameServiceTests
    {
        private const string ContainerId = "userGameDetails";
        private const string PartitionKey = "details";

        [Fact]
        public async Task AddUserGameDetailsAsync_WhenRecordNotExists_ShouldAddRecord()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var streamId = Guid.NewGuid().ToString();
            var recordToAdd = new UserGameDetails
            {
                Id = "",
                StreamId = streamId,
                GameScore = 1234,
                UserId = userId,
                IsUserQualifiedToMoneyGame = true,
                StreamStartDate = DateTime.UtcNow,
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<UserGameDetailsDocument>, IQueryable<UserGameDetailsDocument>>>()))
                .ReturnsAsync(new List<UserGameDetailsDocument>());

            cosmosDbRepository.Setup(e => e.UpsertItemAsync(It.IsAny<string>(), It.IsAny<UserGameDetailsDocument>()))
                .ReturnsAsync(recordToAdd.Adapt<UserGameDetailsDocument>());

            var dateTimeService = new Mock<IDateTimeService>();

            var userGameService = new UserGameService(cosmosDbRepository.Object, dateTimeService.Object);

            // Act
            var addedRecord = await userGameService.AddUserGameDetailsAsync(recordToAdd);

            // Assert
            Assert.NotNull(addedRecord);
            Assert.Equal(addedRecord.StreamId, recordToAdd.StreamId);
            Assert.Equal(addedRecord.UserId, recordToAdd.UserId);
            Assert.Equal(addedRecord.GameScore, recordToAdd.GameScore);
            Assert.Equal(addedRecord.IsUserQualifiedToMoneyGame, recordToAdd.IsUserQualifiedToMoneyGame);
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.IsAny<UserGameDetailsDocument>()), Times.Once());
        }

        [Fact]
        public async Task AddUserGameDetailsAsync_WhenRecordExists_ShouldThrowError()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var streamId = Guid.NewGuid().ToString();
            var recordToAdd = new UserGameDetails
            {
                Id = "",
                StreamId = streamId,
                GameScore = 1234,
                UserId = userId,
                IsUserQualifiedToMoneyGame = true,
                StreamStartDate = DateTime.UtcNow,
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<UserGameDetailsDocument>, IQueryable<UserGameDetailsDocument>>>()))
                .ReturnsAsync(new List<UserGameDetailsDocument>
                {
                    recordToAdd.Adapt<UserGameDetailsDocument>()
                });

            var dateTimeService = new Mock<IDateTimeService>();

            var userGameService = new UserGameService(cosmosDbRepository.Object, dateTimeService.Object);

            // Act

            // Assert
            var exception = await Assert.ThrowsAsync<ValidationException>(async () => await userGameService.AddUserGameDetailsAsync(recordToAdd));
            Assert.Equal("Record with the same stream id and user id already exists", exception.Message);
            cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.IsAny<UserGameDetailsDocument>()), Times.Never());
        }

        [Fact]
        public async Task GetUserGameDetailsByStreams_WhenRecordsExists_ShouldReturnData()
        {
            // Arrange
            var streamIds = new[]
            {
                Guid.NewGuid().ToString(),
                Guid.NewGuid().ToString()
            };
            var firstDocumentId = Guid.NewGuid().ToString();
            var secondDocumentId = Guid.NewGuid().ToString();
            var userId = Guid.NewGuid().ToString();
            var documents = new List<UserGameDetailsDocument>
            {
                new UserGameDetailsDocument
                {
                    Id = firstDocumentId,
                    StreamId = streamIds.First(),
                    UserId = userId,
                    GameScore = 1230,
                    IsUserQualifiedToMoneyGame = true,
                    StreamStartDate = DateTime.UtcNow,
                },
                new UserGameDetailsDocument
                {
                    Id = secondDocumentId,
                    StreamId = streamIds.Last(),
                    UserId = userId,
                    GameScore = 230,
                    IsUserQualifiedToMoneyGame = false,
                    StreamStartDate = DateTime.UtcNow,
                }
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository
                .Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<UserGameDetailsDocument>, IQueryable<UserGameDetailsDocument>>>())).ReturnsAsync(documents);

            var dateTimeService = new Mock<IDateTimeService>();

            var userGameService = new UserGameService(cosmosDbRepository.Object, dateTimeService.Object);

            // Act
            var result = await userGameService.GetUserGameDetailsByStreams(streamIds);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.FirstOrDefault());
            Assert.Equal(result.First().StreamId, streamIds.First());
            Assert.NotNull(result.LastOrDefault());
            Assert.Equal(result.Last().StreamId, streamIds.Last());
            cosmosDbRepository.Verify(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<UserGameDetailsDocument>, IQueryable<UserGameDetailsDocument>>>()), Times.Once());
        }

        public async Task GetAllTimeUserGameDetails_WhenRecordsExists_ShouldReturnData()
        {
            // Arrange
            var limit = 5;
            var skip = 0;
            var userId = Guid.NewGuid().ToString();
            var userScoreDetails = new UserScoreDetails[]
            {
                new UserScoreDetails
                {
                    UserId = userId,
                    TotalScore = 1230
                },
                new UserScoreDetails
                {
                    UserId = userId,
                    TotalScore = 230
                }
            };

            var cosmosDbRepository = new Mock<ICosmosDbRepository>();
            cosmosDbRepository
                .Setup(e => e.CallStoredProcedure<UserScoreDetails[]>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(userScoreDetails);

            var dateTimeService = new Mock<IDateTimeService>();

            var userGameService = new UserGameService(cosmosDbRepository.Object, dateTimeService.Object);

            // Act
            var result = await userGameService.GetAllTimeUserGameDetails(limit, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(result.Count(), userScoreDetails.Length);
        }
    }
}
