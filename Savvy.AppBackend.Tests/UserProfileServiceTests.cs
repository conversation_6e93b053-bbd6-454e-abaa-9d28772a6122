using System.Net;
using Mapster;
using Microsoft.Azure.Cosmos;
using Moq;
using Savvy.AppBackend.Application.Services;
using Savvy.AppBackend.Data.CosmosDb;
using Savvy.AppBackend.Data.CosmosDb.Documents;
using Savvy.AppBackend.Domain.Exceptions;
using Savvy.AppBackend.Domain.Models;
using Savvy.AppBackend.Domain.Models.UserProfile;
using Savvy.AppBackend.Domain.Services;

namespace Savvy.AppBackend.Tests;

public class UserProfileServiceTests
{
    private const string ContainerId = "users";

    [Fact]
    public async Task CreateUserProfile_Can_CreateUserProfile()
    {
        // Arrange
        var userId = "userID";
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();
        var userToCreate = new UserProfile
        {
            Username = "Test",
            Email = "<EMAIL>",
            ImageUrl = "image",
            Id = userId
        };

        cosmosDbRepository.Setup(e => e.ListItemsAsync(ContainerId, It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>()))
            .ReturnsAsync([]);
        cosmosDbRepository.Setup(e => e.UpsertItemAsync(ContainerId, It.IsAny<UserProfileDocument>()))
            .ReturnsAsync(userToCreate.Adapt<UserProfileDocument>());

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        var userProfile = await userProfileService.CreateUserProfileAsync(userToCreate);

        // Assert
        Assert.NotNull(userProfile);
        Assert.Equal(userId, userProfile.Id);
        Assert.Equal("Test", userProfile.Username);
        Assert.Equal("<EMAIL>", userProfile.Email);
    }

    [Fact]
    public async Task CreateUserProfile_Can_Not_CreateUserProfile_UserWitGivenUsernameAlreadyExists()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var newUserId = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(ContainerId, It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>()))
            .ReturnsAsync([new UserProfileDocument { Email = "<EMAIL>", ImageUrl = "image", Username = "Test", Id = userId }]);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act, Assert
        await Assert.ThrowsAsync<ValidationException>(async () => await userProfileService.CreateUserProfileAsync(new UserProfile
        {
            Username = "Test",
            Email = "<EMAIL>",
            ImageUrl = "image",
            Id = newUserId
        }));
    }

    [Fact]
    public async Task CreateUserProfile_Can_Not_CreateUserProfile_UserWitGivenUserIdAlreadyExists()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var username = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(ContainerId, It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>()))
            .ReturnsAsync([new UserProfileDocument { Email = "<EMAIL>", ImageUrl = "image", Username = "Test", Id = userId }]);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act, Assert
        await Assert.ThrowsAsync<ValidationException>(async () => await userProfileService.CreateUserProfileAsync(new UserProfile
        {
            Username = username,
            ImageUrl = "image",
            Email = "<EMAIL>",
            Id = userId
        }));
    }

    [Fact]
    public async Task GetUserProfileById_Can_ReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();
        userGameService.Setup(e => e.GetUserGameDetailsByStreamsAndUser(It.IsAny<string[]>(), It.IsAny<string>())).ReturnsAsync(new[]
        {
            new UserGameDetails
            {
                Id = "Id1",
                StreamId = "streamId1",
                GameScore =  120,
                IsUserQualifiedToMoneyGame = false,
                UserId = userId,
                StreamStartDate = DateTime.UtcNow,
            },
            new UserGameDetails
            {
                Id = "Id2",
                StreamId = "streamId2",
                GameScore =  5120,
                IsUserQualifiedToMoneyGame = true,
                UserId = userId,
                StreamStartDate = DateTime.UtcNow,
            }
        });

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<UserProfileDocument>(ContainerId, userId, userId))
            .ReturnsAsync(new UserProfileDocument
            {
                Username = "Test",
                Email = "<EMAIL>",
                ImageUrl = "image",
                Id = userId
            });

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        var userProfile = await userProfileService.GetUserProfileByIdAsync(userId);

        // Assert
        Assert.NotNull(userProfile);
        Assert.Equal(userId, userProfile.Id);
        Assert.Equal("Test", userProfile.Username);
        Assert.Equal(5240, userProfile.Score);
        Assert.Equal("<EMAIL>", userProfile.Email);
    }

    [Fact]
    public async Task GetUserProfileById_Can_Not_ReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<UserProfileDocument>(ContainerId, userId, userId))
            .ThrowsAsync(new CosmosException("message", HttpStatusCode.NotFound, 404, "activityId", 100));
        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act, Assert
        await Assert.ThrowsAsync<NotFoundException>(
            async () => await userProfileService.GetUserProfileByIdAsync(userId));
    }

    [Fact]
    public async Task GetUserProfileByUsername_Can_ReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var username = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();
        userGameService.Setup(e => e.GetUserGameDetailsByStreamsAndUser(It.IsAny<string[]>(), It.IsAny<string>())).ReturnsAsync(new[]
        {
            new UserGameDetails
            {
                Id = "Id1",
                StreamId = "streamId1",
                GameScore =  120,
                IsUserQualifiedToMoneyGame = false,
                UserId = userId,
                StreamStartDate = DateTime.UtcNow,
            },
            new UserGameDetails
            {
                Id = "Id2",
                StreamId = "streamId2",
                GameScore =  5120,
                IsUserQualifiedToMoneyGame = true,
                UserId = userId,
                StreamStartDate = DateTime.UtcNow,
            }
        });

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(ContainerId, It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>()))
            .ReturnsAsync([new UserProfileDocument { Email = "<EMAIL>", ImageUrl = "image", Username = username, Id = userId }]);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        var userProfile = await userProfileService.GetUserProfileByUsernameAsync(username);

        // Assert
        Assert.NotNull(userProfile);
        Assert.Equal(userId, userProfile.Id);
        Assert.Equal(username, userProfile.Username);
        Assert.Equal(5240, userProfile.Score);
        Assert.Equal("<EMAIL>", userProfile.Email);
    }

    [Fact]
    public async Task GetUserProfileByUsername_Can_Not_ReturnUserProfile()
    {
        // Arrange
        var userId = Guid.NewGuid().ToString();
        var username = Guid.NewGuid().ToString();
        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();
        
        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(ContainerId, It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>()))
            .ReturnsAsync([]);
        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act, Assert
        await Assert.ThrowsAsync<NotFoundException>(
            async () => await userProfileService.GetUserProfileByUsernameAsync(username));
    }

    [Fact]
    public async Task GetListOfUsersByIdAsync_WhenUsersExists_ReturnListOfUsers()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var secondUserId = Guid.NewGuid().ToString();
        var users = new List<UserProfileDocument>
        {
            new UserProfileDocument
            {
                Id = firstUserId,
                Username = "Username1",
                ImageUrl = "ImageUrl1",
                Email = "Email1"
            },
            new UserProfileDocument
            {
                Id = secondUserId,
                Username = "Username2",
                ImageUrl = "ImageUrl2",
                Email = "Email2"
            }
        };

        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ListItemsAsync(It.IsAny<string>(), It.IsAny<Func<IOrderedQueryable<UserProfileDocument>, IQueryable<UserProfileDocument>>>())).ReturnsAsync(users);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        var result = await userProfileService.GetListOfUsersByIdAsync([firstUserId, secondUserId]);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(firstUserId, result.First().Id);
        Assert.Equal(secondUserId, result.Last().Id);
        
    }

    [Fact]
    public async Task UpdateSparksNumber_WhenSparksToAddArePassed_ShouldIncreaseSparksNumber()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var sparksToAdd = 35;
        var user = new UserProfileDocument
        {
            Id = firstUserId,
            Username = "Username1",
            ImageUrl = "ImageUrl1",
            Email = "Email1",
            Sparks = 0
        };
        var expectedSparksNumber = user.Sparks + sparksToAdd;

        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<UserProfileDocument>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(user);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        var result = await userProfileService.UpdateSparksNumber(sparksToAdd, firstUserId);

        // Assert
        cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<UserProfileDocument>(e => e.Sparks == expectedSparksNumber)), Times.Once());
    }

    [Fact]
    public async Task UpdateSparksNumber_WhenSparksToAddArePassed_ShouldDescreaseSparksNumber()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var sparksToAdd = -35;
        var user = new UserProfileDocument
        {
            Id = firstUserId,
            Username = "Username1",
            ImageUrl = "ImageUrl1",
            Email = "Email1",
            Sparks = 100
        };
        var expectedSparksNumber = user.Sparks + sparksToAdd;

        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<UserProfileDocument>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(user);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act
        await userProfileService.UpdateSparksNumber(sparksToAdd, firstUserId);

        // Assert
        cosmosDbRepository.Verify(e => e.UpsertItemAsync(It.IsAny<string>(), It.Is<UserProfileDocument>(e => e.Sparks == expectedSparksNumber)), Times.Once());
    }

    [Fact]
    public async Task UpdateSparksNumber_WhenSparksNumberIsZero_ShouldThrowValidationException()
    {
        // Arrange
        var firstUserId = Guid.NewGuid().ToString();
        var sparksToAdd = -35;
        var user = new UserProfileDocument
        {
            Id = firstUserId,
            Username = "Username1",
            ImageUrl = "ImageUrl1",
            Email = "Email1",
            Sparks = 0
        };

        var streamService = new Mock<IStreamService>();
        var userGameService = new Mock<IUserGameService>();

        var cosmosDbRepository = new Mock<ICosmosDbRepository>();
        cosmosDbRepository.Setup(e => e.ReadItemAsync<UserProfileDocument>(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>())).ReturnsAsync(user);

        var userProfileService = new UserProfileService(cosmosDbRepository.Object, streamService.Object, userGameService.Object);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ValidationException>(async () => await userProfileService.UpdateSparksNumber(sparksToAdd, firstUserId));
        Assert.Equal("The result of sparks adding is less then zero. Sparks number can't be less then 0.", exception.Message);
    }
}